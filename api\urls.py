from django.urls import path
from api.catastro.views import ProvinciasView, ListarMunicipioView, ListarViasView
from api.buscar_provincia import BuscarProvinciaView
from api.coaching.coaching import CoachingInsertView
from api.coaching.coaching import CoachingFiltrarRangoView
from api.coaching.coaching import CoachingFiltrarPorFechaView
from api.coaching.coaching import CoachingUpdateView
from api.coaching.coaching import CoachingDeleteView
from api.coaching.asesor_listar import ListarUsuariosAsesorView
from api.coaching.pregunta_edita import PreguntaDeleteView, OpcionesDeleteView, RespuestasDeleteView, PreguntaCreateView, PreguntaUpdateView, OpcionRespuestaCreateView, OpcionRespuestaUpdateView, OpcionesDeletePorIdView
from api.coaching.ventas import VentasPorAsesorView
from api.coaching.sede import ListarSedesView   
from api.coaching.respuestas_encuesta import RespuestasEncuestaView   
from api.coaching.respuestas_encuesta import RespuestasTextoLibreView
from api.coaching.respuestas_encuesta import EstadisticasEncuestaView

#TRELLO
from api.coaching.trello_tablero_view import (
    TableroCreateView,
    TableroListView,
    TableroListIdView,
    TableroUpdateView,
    TableroInactivarView,
)

from api.coaching.trello_listas_view import (
    ListaCrearView,
    ListaListarPorTableroView,
    ListaEditarView,
    ListaEliminarView,
    ListaListarTareasPorTableroView,
    TareaMoverView
)

from api.coaching.trello_tarea_view import (
    TareaCrearView,
    TareaListarPorListaView,
    TareaEditarView,
    TareaEliminarView
    )

from api.coaching.trello_tarea_asesor import(
    ListarListasConTareasView,
    RegistrarMovimientoTareaView,
    ListarTareasPorUsuarioView,
    ListarListasYMovimientosView,
)

from api.coaching.trello_reportes import(
        TableroListarActivosView,
        TareasPorTablero,
        UsuariosAsignadosPorTareaView,
)

from api.coaching.tareas_v2 import(
        ListasConTareasView,
        CrearTareaView,
        EditarTareaView,
        DetalleTareaView,
        ListarTareasAsignadasView,
        CrearHistorialTareaView,
        ContarTareasAsignadasView,
        ResumenEncuestaUsuarioView,
        
)

urlpatterns = [
    path('catastro/provincias/', ProvinciasView.as_view(), name='listar_provincias'),
    path('catastro/municipios/', ListarMunicipioView.as_view(), name='listar_municipios'),
    path('catastro/vias/', ListarViasView.as_view(), name='listar_vias'),
    path('buscar-direccion/', BuscarProvinciaView.as_view(), name='buscar_provincia'),
    path('insertar-frase/', CoachingInsertView.as_view(), name='insertar_frase'),
    path('frases-por-rango/', CoachingFiltrarRangoView.as_view(), name='frases_por_rango'),
    path('frases-por-fecha/', CoachingFiltrarPorFechaView.as_view(), name='frases_por_fecha'),
    path('editar-frase/', CoachingUpdateView.as_view(), name='editar_frase'),
    path('eliminar-frase/', CoachingDeleteView.as_view(), name='eliminar_frase'),
    path('usuarios-asesores/', ListarUsuariosAsesorView.as_view(), name='listar_usuarios_asesores'),
    path('pregunta_eliminar/', PreguntaDeleteView.as_view(), name='eliminar_pregunta'),
    path('opciones_eliminar/', OpcionesDeleteView.as_view(), name='eliminar_opciones'),
    path('respuestas_eliminar/', RespuestasDeleteView.as_view(), name='eliminar_respuestas'),
    path('pregunta_crear/', PreguntaCreateView.as_view(), name='crear_pregunta'),
    path('pregunta_actualizar/', PreguntaUpdateView.as_view(), name='actualizar_pregunta'),
    path('opcion_crear/', OpcionRespuestaCreateView.as_view(), name='crear_opcion'),
    path('opcion_actualizar/', OpcionRespuestaUpdateView.as_view(), name='actualizar_opcion'),
    path('opcion_eliminar_id/', OpcionesDeletePorIdView.as_view(), name='eliminar_opcion_por_id'),
    path('ventas-por-asesor/', VentasPorAsesorView.as_view(), name='ventas_por_asesor'),
    path('sedes-listar/', ListarSedesView.as_view(), name='listar_sedes'),
    path('respuestas_encuesta/', RespuestasEncuestaView.as_view(), name='respuestas_encuesta'),
    path('respuestas_texto_libre/', RespuestasTextoLibreView.as_view(), name='respuestas_texto_libre'),
    path('respuestas_total/', EstadisticasEncuestaView.as_view(), name='EncuestaDetalleView'),



    #trello
    path('tablero/crear/', TableroCreateView.as_view(), name='crear_tablero'),
    path('tablero/listar/', TableroListView.as_view(), name='listar_tableros'),
    path('tablero/listar-id/', TableroListIdView.as_view(), name='listar_tablero_por_id'),
    path('tablero/editar/', TableroUpdateView.as_view(), name='editar_tablero'),
    path('tablero/inactivar/', TableroInactivarView.as_view(), name='inactivar_tablero'),

    path('lista/crear/', ListaCrearView.as_view(), name='crear_lista'),
    path('lista/listar/', ListaListarPorTableroView.as_view(), name='listar_listas_por_tablero/'),
    path('lista/editar/', ListaEditarView.as_view(), name='editar_lista'),
    path('lista/eliminar/', ListaEliminarView.as_view(), name='eliminar_lista'),
    path('lista/listas-tareas/', ListaListarTareasPorTableroView.as_view(), name='listar_tareas_por_tablero'),
    path('tarea/mover/', TareaMoverView.as_view(), name='mover_tarea'),

    path('tarea/crear/', TareaCrearView.as_view(), name='crear_tarea'),
    path('tarea/listar/', TareaListarPorListaView.as_view(), name='listar_tareas_por_lista'),
    path('tarea/editar/', TareaEditarView.as_view(), name='editar_tarea'),
    path('tarea/eliminar/', TareaEliminarView.as_view(), name='eliminar_tarea'),

    #asesor
    path('asesor/tareas-por-hacer/', ListarListasYMovimientosView.as_view(), name = 'listar-por-hacer'),
    path('tarea/movimiento/registrar/', RegistrarMovimientoTareaView.as_view()),
    path('tarea/movimiento/listar/', ListarTareasPorUsuarioView.as_view()),
    path('tarea/movimiento/listarTodo/', ListarListasYMovimientosView.as_view()),

    #reportes
    path('reporte/tableros-activos/', TableroListarActivosView.as_view()),
    path('reporte/tareas-activas/', TareasPorTablero.as_view()),
    path('reporte/usuarios-asignados/', UsuariosAsignadosPorTareaView.as_view()),

    #tareas_v2
    path('tareas_v2/listas_con_tareas/', ListasConTareasView.as_view(), name='listas_con_tareas'),
    path('tareas_v2/crear/', CrearTareaView.as_view()),
    path('tareas_v2/editar/<int:id_tarea>/', EditarTareaView.as_view()),
    path('tarea/detalle/<int:id_tarea>', DetalleTareaView.as_view(), name='listas_con_tareas_con_estado'),
    path('tareas_v2/tareas_asignadas/<int:id_usuario>', ListarTareasAsignadasView.as_view(), name='tareas_asignadas'),
    path('tareas_v2/historial', CrearHistorialTareaView.as_view(), name='crear_historial_tarea'),
    path('tareas_v2/contar_tareas_asignadas/<int:id_usuario>', ContarTareasAsignadasView.as_view(), name='contar_tareas_asignadas'),
    path('tareas_v2/resumen_encuesta_usuario/<int:id_usuario>', ResumenEncuestaUsuarioView.as_view(), name='resumen_encuesta_usuario'),
]
