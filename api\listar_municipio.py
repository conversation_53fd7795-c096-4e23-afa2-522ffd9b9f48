from django.http import JsonResponse
from django.views import View
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
import json
from django.db import connection

@method_decorator(csrf_exempt, name='dispatch')
class ListarMunicipioView(View):
    def post(self, request):
        try:
            data = json.loads(request.body.decode('utf-8'))
            id_provincia = data.get('id_provincia')
        except (json.JSONDecodeError, UnicodeDecodeError):
            return JsonResponse({'error': 'Formato JSON inválido'}, status=400)

        if not id_provincia:
            return JsonResponse({'error': 'El campo "id_provincia" es requerido'}, status=400)

        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT id_municipio, descripcion
                FROM municipios
                WHERE id_provincia = %s AND estado = 'A'
                ORDER BY id_municipio ASC
            """, [id_provincia])
            columns = [col[0] for col in cursor.description]
            results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        return JsonResponse(results, safe=False)
