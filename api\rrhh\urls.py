from django.urls import path
from api.rrhh.views import TestRRHHView
from api.rrhh.empresa import EmpresaView
from api.rrhh.personal import (
    listar_personal_general,
    listar_personal_por_id,
    crear_personal,
    actualizar_personal
)
from api.rrhh.contrato import (
    listar_contratos_por_personal,
    obtener_contrato_por_id,
    crear_contrato,
    actualizar_contrato,
    eliminar_contrato,
    actualizar_ruta_archivo_contrato
)
from api.rrhh.vacaciones import (
    listar_vacaciones_por_personal_anio,
    obtener_vacacion_por_id,
    crear_vacacion,
    actualizar_vacacion,
    eliminar_vacacion,
    resumen_vacaciones_personal,
    listado_personal_vacaciones
)

urlpatterns = [
    path('test/', TestRRHHView.as_view(), name='test_rrhh'),
    path('empresa/', EmpresaView.as_view(), name='empresa_list_create'),
    path('empresa/<int:id_empresa>/', EmpresaView.as_view(), name='empresa_detail'),
    path('personal/general/', listar_personal_general, name='listar_personal_general'),
    path('personal/<int:id_personal>/', listar_personal_por_id, name='listar_personal_por_id'),
    path('personal/crear/', crear_personal, name='crear_personal'),
    path('personal/actualizar/<int:id_personal>/', actualizar_personal, name='actualizar_personal'),

    # Rutas para contratos
    path('contrato/personal/<int:id_personal>/', listar_contratos_por_personal, name='listar_contratos_por_personal'),
    path('contrato/<int:id_contrato>/', obtener_contrato_por_id, name='obtener_contrato_por_id'),
    path('contrato/crear/', crear_contrato, name='crear_contrato'),
    path('contrato/actualizar/<int:id_contrato>/', actualizar_contrato, name='actualizar_contrato'),
    path('contrato/eliminar/<int:id_contrato>/', eliminar_contrato, name='eliminar_contrato'),
    path('contrato/actualizar-archivo/<int:id_contrato>/', actualizar_ruta_archivo_contrato, name='actualizar_ruta_archivo_contrato'),

    # Rutas para vacaciones
    path('vacaciones/personal/<int:id_personal>/anio/<int:anio_seleccionado>/', listar_vacaciones_por_personal_anio, name='listar_vacaciones_por_personal_anio'),
    path('vacaciones/<int:id_vacaciones_personal>/', obtener_vacacion_por_id, name='obtener_vacacion_por_id'),
    path('vacaciones/crear/', crear_vacacion, name='crear_vacacion'),
    path('vacaciones/actualizar/<int:id_vacaciones_personal>/', actualizar_vacacion, name='actualizar_vacacion'),
    path('vacaciones/eliminar/<int:id_vacaciones_personal>/', eliminar_vacacion, name='eliminar_vacacion'),
    path('vacaciones/resumen/<int:id_personal>/anio/<int:anio_seleccionado>/', resumen_vacaciones_personal, name='resumen_vacaciones_personal'),
    path('vacaciones/listado-personal/<int:anio>/', listado_personal_vacaciones, name='listado_personal_vacaciones'),
]