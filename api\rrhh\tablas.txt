CREATE TABLE personal (
    id_personal INT AUTO_INCREMENT PRIMARY KEY,
    id_empresa INT NOT NULL,
    primer_nombre VARCHAR(100),
    segundo_nombre VARCHAR(100),
    apellido_paterno VARCHAR(100),
    apellido_materno VARCHAR(100),
    tipo_documento varchar(30),
    documento VARCHAR(20),
    apellidos_nombres_completos VARCHAR(200),
    tipo_contrato VARCHAR(100),
    fecha_incorporacion DATE,
    fecha_cese DATE,
    fecha_nacimiento DATE,
    edad INT,
    direccion VARCHAR(200),
    telefono_movil VARCHAR(20),
    correo_electronico VARCHAR(150),
    cargo VARCHAR(100),
    sistema_pensiones VARCHAR(100),
    nombre_institucion_educativa VARCHAR(200),
    carrera_profesional VARCHAR(150),
    institucion_bancaria VARCHAR(100),
    cuenta_sueldo VARCHAR(50),
    horario VARCHAR(100),
    salario DECIMAL(10,2),
    condicion_trabajo_alimentacion VARCHAR(100),
    break_time VARCHAR(50),
    asignacion_familiar BOOLEAN,
    importe DECIMAL(10,2),
    motivo_cese VARCHAR(150),
    importe_liquidacion DECIMAL(10,2),
    genero VARCHAR(10),
    coordinador VARCHAR(100),
    tiempo_servicio VARCHAR(50),
    inicio_contrato DATE,
    fin_contrato DATE,
    fecha_vencimiento_periodo_prueba DATE,
    digito_verificador VARCHAR(10),
    respuesta_rit VARCHAR(50),
    confidencialidad BOOLEAN,
    carga_familiar INT,
    legalizacion_mapfre BOOLEAN,
    FOREIGN KEY (id_empresa) REFERENCES empresa(id_empresa)
);


DATO INSERTADO:


INSERT INTO personal (
    id_empresa,
    primer_nombre,
    segundo_nombre,
    apellido_paterno,
    apellido_materno,
    tipo_documento,
    documento,
    apellidos_nombres_completos,
    tipo_contrato,
    fecha_incorporacion,
    fecha_nacimiento,
    edad,
    direccion,
    telefono_movil,
    correo_electronico,
    cargo,
    sistema_pensiones,
    nombre_institucion_educativa,
    carrera_profesional,
    institucion_bancaria,
    cuenta_sueldo,
    horario,
    salario,
    importe,
    break_time,
    asignacion_familiar,
    genero,
    tiempo_servicio,
    inicio_contrato,
    fin_contrato,
    digito_verificador,
    respuesta_rit,
    confidencialidad,
    carga_familiar,
    legalizacion_mapfre
) VALUES (
    7, -- id_empresa
    'KHATERIN',
    'NATHALY',
    'ABAD',
    'CORONADO',
    'DNI',
    '74593277',
    'ABAD CORONADO KHATERIN NATHALY',
    'PLANILLA',
    '2025-08-01',
    '1999-03-06',
    26,
    'AV.CAJAMARCA Nº 660 AMPL. 9 DE OCTUBRE',
    '923091243',
    '<EMAIL>',
    'BACK OFFICE',
    'AFP INTEGRA',
    'I.S.T.P REPUBLICA FEDERAL DE ALEMANIA',
    'ELECTRONICA INDUSTRIAL',
    'BBVA',
    '001108140230560981',
    'FULL TIME',
    1130.00,
    270.00,
    '45 MNTS',
    FALSE,         -- asignacion_familiar ('NO')
    'FEMENINO',
    '3 AÑO 0 MESES 5 DIAS',
    '2025-07-01',
    '2025-07-31',
    '6',
    TRUE,          -- respuesta_rit ('SI')
    TRUE,          -- confidencialidad ('SI')
    0,
    TRUE           -- legalizacion_mapfre ('SI')
);
