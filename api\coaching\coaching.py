from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.db import connection
import json
from datetime import datetime

@method_decorator(csrf_exempt, name='dispatch')
class CoachingInsertView(View):
    def post(self, request):
        try:
            body = json.loads(request.body)

            nombre = body.get('nombre')
            fecha = body.get('fecha') or datetime.now().date().isoformat()
            imagen = body.get('imagen')
            estado = body.get('estado', 'A')

            if not nombre or not imagen:
                return JsonResponse({'error': 'nombre e imagen son requeridos'}, status=400)

            with connection.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO coaching_motivacion (nombre, fecha, imagen, estado)
                    VALUES (%s, %s, %s, %s)
                """, [nombre, fecha, imagen, estado])

            return JsonResponse({'mensaje': 'Frase insertada correctamente'}, status=201)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')        
class CoachingFiltrarRangoView(View):
    def post(self, request):
        try:
            body = json.loads(request.body)
            desde = body.get('desde')
            hasta = body.get('hasta')

            if not desde or not hasta:
                return JsonResponse({'error': 'Ambas fechas (desde, hasta) son requeridas'}, status=400)

            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM coaching_motivacion 
                    WHERE fecha BETWEEN %s AND %s
                    ORDER BY fecha DESC
                """, [desde, hasta])
                columns = [col[0] for col in cursor.description]
                results = [dict(zip(columns, row)) for row in cursor.fetchall()]

            return JsonResponse(results, safe=False, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class CoachingFiltrarPorFechaView(View):
    def post(self, request):
        try:
            body = json.loads(request.body)
            fecha = body.get('fecha')

            if not fecha:
                return JsonResponse({'error': 'La fecha es requerida'}, status=400)

            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM coaching_motivacion 
                    WHERE fecha = %s
                    ORDER BY id DESC
                """, [fecha])
                columns = [col[0] for col in cursor.description]
                results = [dict(zip(columns, row)) for row in cursor.fetchall()]

            return JsonResponse(results, safe=False, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
        

@method_decorator(csrf_exempt, name='dispatch')
class CoachingUpdateView(View):
    def post(self, request):
        try:
            body = json.loads(request.body)

            id = body.get('id')
            nombre = body.get('nombre')
            fecha = body.get('fecha') or datetime.now().date().isoformat()
            imagen = body.get('imagen')

            if not id:
                return JsonResponse({'error': 'ID es requerido para actualizar'}, status=400)
            if not nombre or not imagen:
                return JsonResponse({'error': 'nombre e imagen son requeridos'}, status=400)

            with connection.cursor() as cursor:
                cursor.execute("""
                    UPDATE coaching_motivacion
                    SET nombre = %s,
                        fecha = %s,
                        imagen = %s
                    WHERE id = %s
                """, [nombre, fecha, imagen, id])

            return JsonResponse({'mensaje': 'Frase actualizada correctamente'}, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class CoachingDeleteView(View):
    def post(self, request):
        try:
            body = json.loads(request.body)
            id = body.get('id')

            if not id:
                return JsonResponse({'error': 'ID es requerido para eliminar'}, status=400)

            with connection.cursor() as cursor:
                cursor.execute("DELETE FROM coaching_motivacion WHERE id = %s", [id])

            return JsonResponse({'mensaje': 'Frase eliminada correctamente'}, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
