from django.http import JsonResponse
from django.views import View
from django.db import connection

class ProvinciasView(View):
    def get(self, request):
        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM provincias where estado = 'A'")
            columns = [col[0] for col in cursor.description]
            results = [dict(zip(columns, row)) for row in cursor.fetchall()]
        return JsonResponse(results, safe=False)
