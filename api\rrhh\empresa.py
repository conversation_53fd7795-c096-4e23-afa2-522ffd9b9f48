from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.db import connection
import json

@method_decorator(csrf_exempt, name='dispatch')
class EmpresaView(View):
    def get(self, request):
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT
                        e.id_empresa,
                        e.ruc,
                        e.razon_social,
                        e.nombre_comercial,
                        e.direccion AS direccion_empresa,
                        e.departamento,
                        e.provincia,
                        e.distrito,
                        e.telefono AS telefono_empresa,
                        e.correo AS correo_empresa,
                        e.estado AS estado_empresa,
                        r.id_representante,
                        r.dni,
                        r.nombres,
                        r.apellidos,
                        r.direccion AS direccion_representante,
                        r.telefono AS telefono_representante,
                        r.correo AS correo_representante,
                        r.fecha_cargo,
                        er.tipo_representante
                    FROM empresa e
                    INNER JOIN empresa_representante er ON e.id_empresa = er.id_empresa
                    INNER JOIN representante_legal r ON er.id_representante = r.id_representante;
                """)
                columns = [col[0] for col in cursor.description]
                empresas = [
                    dict(zip(columns, row))
                    for row in cursor.fetchall()
                ]
            return JsonResponse(empresas, safe=False)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    def post(self, request):
        try:
            data = json.loads(request.body)
            empresa_data = data.get('empresa', {})
            representante_data = data.get('representante', {})

            with connection.cursor() as cursor:
                # Start transaction
                cursor.execute("START TRANSACTION;")

                # Insert into empresa table
                cursor.execute(
                    """
                    INSERT INTO empresa (ruc, razon_social, nombre_comercial, direccion, departamento, provincia, distrito, telefono, correo)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """,
                    [
                        empresa_data.get('ruc'),
                        empresa_data.get('razon_social'),
                        empresa_data.get('nombre_comercial'),
                        empresa_data.get('direccion'),
                        empresa_data.get('departamento'),
                        empresa_data.get('provincia'),
                        empresa_data.get('distrito'),
                        empresa_data.get('telefono'),
                        empresa_data.get('correo')
                    ]
                )
                id_empresa = cursor.lastrowid

                # Insert into representante_legal table
                cursor.execute(
                    """
                    INSERT INTO representante_legal (dni, nombres, apellidos, direccion, telefono, correo, cargo, fecha_cargo)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """,
                    [
                        representante_data.get('dni'),
                        representante_data.get('nombres'),
                        representante_data.get('apellidos'),
                        representante_data.get('direccion'),
                        representante_data.get('telefono'),
                        representante_data.get('correo'),
                        representante_data.get('cargo'),
                        representante_data.get('fecha_cargo')
                    ]
                )
                id_representante = cursor.lastrowid

                # Insert into empresa_representante table
                cursor.execute(
                    """
                    INSERT INTO empresa_representante (id_empresa, id_representante, tipo_representante)
                    VALUES (%s, %s, %s)
                    """,
                    [
                        id_empresa,
                        id_representante,
                        data.get('tipo_representante', 'LEGAL')
                    ]
                )

                # Commit transaction
                cursor.execute("COMMIT;")

            return JsonResponse({'message': 'Empresa y representante registrados exitosamente', 'id_empresa': id_empresa, 'id_representante': id_representante}, status=201)

        except Exception as e:
            with connection.cursor() as cursor:
                cursor.execute("ROLLBACK;") # Rollback on error
            return JsonResponse({'error': str(e)}, status=500)

    def put(self, request, id_empresa):
        try:
            data = json.loads(request.body)
            empresa_data = data
            representante_data = data.get('representante', {})

            with connection.cursor() as cursor:
                cursor.execute("START TRANSACTION;")

                # Update empresa table
                update_empresa_query = """
                    UPDATE empresa SET
                        ruc = %s, razon_social = %s, nombre_comercial = %s, direccion = %s,
                        departamento = %s, provincia = %s, distrito = %s, telefono = %s, correo = %s
                    WHERE id_empresa = %s
                """
                cursor.execute(
                    update_empresa_query,
                    [
                        empresa_data.get('ruc'),
                        empresa_data.get('razon_social'),
                        empresa_data.get('nombre_comercial'),
                        empresa_data.get('direccion'),
                        empresa_data.get('departamento'),
                        empresa_data.get('provincia'),
                        empresa_data.get('distrito'),
                        empresa_data.get('telefono'),
                        empresa_data.get('correo'),
                        id_empresa
                    ]
                )

                # Get id_representante from empresa_representante
                cursor.execute("SELECT id_representante FROM empresa_representante WHERE id_empresa = %s", [id_empresa])
                result = cursor.fetchone()
                if result:
                    id_representante = result[0]

                    # Update representante_legal table
                    update_representante_query = """
                        UPDATE representante_legal SET
                            dni = %s, nombres = %s, apellidos = %s, direccion = %s,
                            telefono = %s, correo = %s, cargo = %s, fecha_cargo = %s
                        WHERE id_representante = %s
                    """
                    cursor.execute(
                        update_representante_query,
                        [
                            representante_data.get('dni'),
                            representante_data.get('nombres'),
                            representante_data.get('apellidos'),
                            representante_data.get('direccion'),
                            representante_data.get('telefono'),
                            representante_data.get('correo'),
                            representante_data.get('cargo'),
                            representante_data.get('fecha_cargo'),
                            id_representante
                        ]
                    )

                    # Update tipo_representante in empresa_representante if provided
                    if 'tipo_representante' in data:
                        cursor.execute(
                            "UPDATE empresa_representante SET tipo_representante = %s WHERE id_empresa = %s AND id_representante = %s",
                            [data.get('tipo_representante'), id_empresa, id_representante]
                        )

                cursor.execute("COMMIT;")

            return JsonResponse({'message': 'Empresa y representante actualizados exitosamente'})

        except Exception as e:
            with connection.cursor() as cursor:
                cursor.execute("ROLLBACK;")
            return JsonResponse({'error': str(e)}, status=500)

    def delete(self, request, id_empresa):
        try:
            with connection.cursor() as cursor:
                cursor.execute("START TRANSACTION;")

                # Get id_representante from empresa_representante
                cursor.execute("SELECT id_representante FROM empresa_representante WHERE id_empresa = %s", [id_empresa])
                result = cursor.fetchone()
                if result:
                    id_representante = result[0]

                    # Delete from empresa_representante
                    cursor.execute("DELETE FROM empresa_representante WHERE id_empresa = %s AND id_representante = %s", [id_empresa, id_representante])

                    # Delete from representante_legal
                    cursor.execute("DELETE FROM representante_legal WHERE id_representante = %s", [id_representante])

                # Delete from empresa
                cursor.execute("DELETE FROM empresa WHERE id_empresa = %s", [id_empresa])

                cursor.execute("COMMIT;")

            return JsonResponse({'message': 'Empresa y representante eliminados exitosamente'})

        except Exception as e:
            with connection.cursor() as cursor:
                cursor.execute("ROLLBACK;")
            return JsonResponse({'error': str(e)}, status=500)
