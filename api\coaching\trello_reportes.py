from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.db import connection
import json

@method_decorator(csrf_exempt, name='dispatch')
class TableroListarActivosView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            creado_por = data.get('creado_por')

            if not creado_por:
                return JsonResponse({'error': 'El campo creado_por es obligatorio'}, status=400)

            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT id_tablero AS id, nombre, descripcion, color, creado_por, fecha_creacion
                    FROM tableros
                    WHERE estado = 'A' AND creado_por = %s
                """, [creado_por])
                
                filas = cursor.fetchall()
                columnas = [col[0] for col in cursor.description]
                tableros = [dict(zip(columnas, fila)) for fila in filas]

            return JsonResponse({'tableros': tableros}, status=200)

        except Exception as e:
            import traceback
            print(traceback.format_exc())
            return JsonResponse({'error': str(e)}, status=500)
        

@method_decorator(csrf_exempt, name='dispatch')
class TareasPorTablero(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            id_tablero = data.get('id_tablero')

            if not id_tablero:
                return JsonResponse({'error': 'El campo id_tablero es obligatorio'}, status=400)

            with connection.cursor() as cursor:
                cursor.execute("""
                   select id_tarea, titulo from tareas where id_tablero = %s
                """, [id_tablero])
                
                filas = cursor.fetchall()
                columnas = [col[0] for col in cursor.description]
                tareas = [dict(zip(columnas, fila)) for fila in filas]

            return JsonResponse({'tareas': tareas}, status=200)

        except Exception as e:
            import traceback
            print(traceback.format_exc())
            return JsonResponse({'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class UsuariosAsignadosPorTareaView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            id_tarea = data.get('id_tarea')

            if not id_tarea:
                return JsonResponse({'error': 'id_tarea es obligatorio'}, status=400)

            with connection.cursor() as cursor:
                # Obtener tipo_asignacion y asignado_a
                cursor.execute("""
                    SELECT tipo_asignacion, asignado_a
                    FROM tareas
                    WHERE id_tarea = %s
                """, [id_tarea])
                row = cursor.fetchone()

                if not row:
                    return JsonResponse({'error': 'Tarea no encontrada'}, status=404)

                tipo_asignacion, asignado_a = row
                usuarios = []

                # TODOS = traer todos los usuarios activos
                if tipo_asignacion == 'TODOS':
                    cursor.execute("""
                        SELECT codi_usuario, CONCAT(nombre, ' ', apellido) AS nombre
                        FROM usuarios
                        WHERE estado = 'A' order by nombre asc
                    """)
                    usuarios = [{'id': r[0], 'nombre': r[1]} for r in cursor.fetchall()]

                # COORDINACION = traer usuarios con ese coordinador_id
                elif tipo_asignacion == 'COORDINACION':
                    if asignado_a.strip().isdigit():
                        cursor.execute("""
                            SELECT codi_usuario, CONCAT(nombre, ' ', apellido) AS nombre
                            FROM usuarios
                            WHERE estado = 'A' AND coordinador_id = %s   order by nombre asc
                        """, [int(asignado_a.strip())])
                        usuarios = [{'id': r[0], 'nombre': r[1]} for r in cursor.fetchall()]

                # PERSONAL = uno o varios IDs de usuario
                elif tipo_asignacion == 'PERSONAL':
                    ids = [int(id.strip()) for id in asignado_a.split(',') if id.strip().isdigit()]
                    if ids:
                        if len(ids) == 1:
                            cursor.execute("""
                                SELECT codi_usuario, CONCAT(nombre, ' ', apellido) AS nombre
                                FROM usuarios
                                WHERE estado = 'A' AND codi_usuario = %s  order by nombre asc
                            """, [ids[0]])
                        else:
                            cursor.execute("""
                                SELECT codi_usuario, CONCAT(nombre, ' ', apellido) AS nombre
                                FROM usuarios
                                WHERE estado = 'A' AND codi_usuario IN %s  order by nombre asc
                            """, [tuple(ids)])
                        usuarios = [{'id': r[0], 'nombre': r[1]} for r in cursor.fetchall()]

            return JsonResponse({'usuarios': usuarios}, status=200)

        except Exception as e:
            import traceback
            print(traceback.format_exc())
            return JsonResponse({'error': str(e)}, status=500)
        

