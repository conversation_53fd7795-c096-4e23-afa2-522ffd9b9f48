from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.db import connection
import json
from django.db import transaction

@method_decorator(csrf_exempt, name='dispatch')
class ListaCrearView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            nombre = data.get('nombre')
            tablero_id = data.get('tablero_id')
            estado = data.get('estado')

            if not all([nombre, tablero_id, estado]):
                return JsonResponse({'error': 'Todos los campos son obligatorios'}, status=400)

            with transaction.atomic():
                with connection.cursor() as cursor:
                    # Verificamos que el tablero esté activo
                    cursor.execute("""
                        SELECT creado_por FROM tableros 
                        WHERE id_tablero = %s AND estado = 'A' FOR UPDATE
                    """, [tablero_id])
                    resultado = cursor.fetchone()

                    if not resultado:
                        return JsonResponse({'error': 'Tablero no encontrado o inactivo'}, status=404)

                    # Obtenemos el siguiente valor seguro de orden
                    cursor.execute("""
                        SELECT COALESCE(MAX(orden), 0) + 1 FROM listas 
                        WHERE id_tablero = %s FOR UPDATE
                    """, [tablero_id])
                    orden = cursor.fetchone()[0]

                    # Insertamos la lista
                    cursor.execute("""
                        INSERT INTO listas (nombre, id_tablero, estado, orden)
                        VALUES (%s, %s, %s, %s)
                    """, [nombre, tablero_id, estado, orden])

            return JsonResponse({'mensaje': 'Lista creada correctamente', 'orden': orden}, status=201)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class ListaListarPorTableroView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            tablero_id = data.get('tablero_id')

            if not tablero_id:
                return JsonResponse({'error': 'tablero_id es obligatorio'}, status=400)

            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT id_lista, nombre, estado, orden FROM listas
                    WHERE id_tablero = %s AND estado_vista = 'A'
                """, [tablero_id])
                filas = cursor.fetchall()

                listas = [{'id': fila[0], 'nombre': fila[1], 'estado': fila[2], 'orden':fila[3]} for fila in filas]

            return JsonResponse({'listas': listas}, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
        
@method_decorator(csrf_exempt, name='dispatch')
class ListaListarTareasPorTableroView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            tablero_id = data.get('tablero_id')

            if not tablero_id:
                return JsonResponse({'error': 'tablero_id es obligatorio'}, status=400)

            with connection.cursor() as cursor:
                # Consulta de listas y tareas
                cursor.execute("""
                    SELECT 
                        l.id_lista, l.nombre AS nombre_lista, l.estado, l.orden,
                        t.id_tarea, t.titulo, t.descripcion, t.tipo_asignacion,
                        t.asignado_a, t.creado_por, t.estado AS estado_tarea, t.fecha_creacion
                    FROM listas l
                    LEFT JOIN tareas t ON l.id_lista = t.id_lista
                    WHERE l.id_tablero = %s AND l.estado_vista = 'A'
                    ORDER BY l.orden ASC, t.fecha_creacion ASC
                """, [tablero_id])
                rows = cursor.fetchall()

            listas_dict = {}

            for row in rows:
                id_lista = row[0]
                if id_lista not in listas_dict:
                    listas_dict[id_lista] = {
                        'id': row[0],
                        'nombre': row[1],
                        'estado': row[2],
                        'orden': row[3],
                        'tareas': []
                    }

                id_tarea = row[4]
                if id_tarea:
                    tipo_asignacion = row[7]
                    asignado_a = row[8]
                    asignado_nombre = None

                    # Obtener nombre según el tipo de asignación
                    if tipo_asignacion == 'COORDINACION' and asignado_a:
                        with connection.cursor() as cursor:
                            cursor.execute("SELECT CONCAT(nombre, ' ', apellido) FROM usuarios WHERE codi_usuario = %s", [asignado_a])
                            result = cursor.fetchone()
                            if result:
                                asignado_nombre = result[0]

                    elif tipo_asignacion == 'PERSONAL' and asignado_a:
                        ids = [int(uid) for uid in asignado_a.split(',') if uid.isdigit()]
                        if ids:
                            placeholders = ','.join(['%s'] * len(ids))
                            with connection.cursor() as cursor:
                                cursor.execute(f"""
                                    SELECT GROUP_CONCAT(CONCAT(nombre, ' ', apellido) SEPARATOR ', ')
                                    FROM usuarios WHERE codi_usuario IN ({placeholders})
                                """, ids)
                                result = cursor.fetchone()
                                if result:
                                    asignado_nombre = result[0]

                    elif tipo_asignacion == 'SEDE' and asignado_a:
                        with connection.cursor() as cursor:
                            cursor.execute("SELECT nombre FROM sedes WHERE id = %s", [asignado_a])
                            result = cursor.fetchone()
                            if result:
                                asignado_nombre = result[0]

                    listas_dict[id_lista]['tareas'].append({
                        'id': row[4],
                        'titulo': row[5],
                        'descripcion': row[6],
                        'tipo_asignacion': tipo_asignacion,
                        'asignado_a': asignado_a,
                        'asignado_nombre': asignado_nombre,  # 👈 Aquí se añade el nombre
                        'creado_por': row[9],
                        'estado': row[10],
                        'fecha_creacion': row[11]
                    })

            listas = list(listas_dict.values())

            return JsonResponse({'listas': listas}, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

        
from django.views import View
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import json
from django.db import connection
from datetime import datetime

@method_decorator(csrf_exempt, name='dispatch')
class TareaMoverView(View):
    def put(self, request):
        try:
            data = json.loads(request.body)
            tarea_id = data.get('tarea_id')
            nueva_lista_id = data.get('nueva_lista_id')
            lista_origen_id = data.get('lista_origen_id')
            usuario_id = data.get('usuario_id')

            if not all([tarea_id, nueva_lista_id, lista_origen_id, usuario_id]):
                return JsonResponse({'error': 'Faltan datos obligatorios'}, status=400)

            now = datetime.now()

            with connection.cursor() as cursor:
                # 1. Actualizar la tarea
                cursor.execute("""
                    UPDATE tareas
                    SET id_lista = %s, updated_at = %s
                    WHERE id_tarea = %s
                """, [nueva_lista_id, now, tarea_id])

                if cursor.rowcount == 0:
                    return JsonResponse({'error': 'Tarea no encontrada'}, status=404)

                # 2. Registrar el historial de movimiento
                cursor.execute("""
                    INSERT INTO historial_movimientos (tarea_id, lista_origen_id, lista_destino_id, usuario_id, fecha_movimiento)
                    VALUES (%s, %s, %s, %s, %s)
                """, [tarea_id, lista_origen_id, nueva_lista_id, usuario_id, now])

                # 3. Obtener datos actualizados de la tarea
                cursor.execute("""
                    SELECT id_tarea, titulo, id_lista, creado_por, created_at, updated_at
                    FROM tareas
                    WHERE id_tarea = %s
                """, [tarea_id])
                row = cursor.fetchone()

            tarea_actualizada = {
                'id_tarea': row[0],
                'titulo': row[1],
                'id_lista': row[2],
                'creado_por': row[3],
                'created_at': row[4],
                'updated_at': row[5]
            }

            return JsonResponse({
                'mensaje': 'Tarea movida y registrada en historial',
                'tarea': tarea_actualizada
            }, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)



@method_decorator(csrf_exempt, name='dispatch')
class ListaEditarView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            id_lista = data.get('id_lista')
            nuevo_nombre = data.get('titulo')

            if not all([id_lista, nuevo_nombre]):
                return JsonResponse({'error': 'id_lista y nombre son obligatorios'}, status=400)

            with connection.cursor() as cursor:
                cursor.execute("""
                    UPDATE listas SET nombre = %s WHERE id_lista = %s
                """, [nuevo_nombre, id_lista])

            return JsonResponse({'mensaje': 'Lista actualizada correctamente'}, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class ListaEliminarView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            id_lista = data.get('id_lista')

            if not id_lista:
                return JsonResponse({'error': 'id_lista es obligatorio'}, status=400)

            with connection.cursor() as cursor:
                cursor.execute("""
                    UPDATE listas SET estado = 'I' WHERE id_lista = %s
                """, [id_lista])

            return JsonResponse({'mensaje': 'Lista eliminada (inactiva) correctamente'}, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
