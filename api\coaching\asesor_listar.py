from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.db import connection
import json

@method_decorator(csrf_exempt, name='dispatch')
class ListarUsuariosAsesorView(View):
    def get(self, request):
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT 
                        codi_usuario, 
                        apellido, 
                        nombre
                    FROM usuarios
                    WHERE  estado = 'A'
                    ORDER BY apellido ASC
                """)
                columns = [col[0] for col in cursor.description]
                results = [dict(zip(columns, row)) for row in cursor.fetchall()]

            return JsonResponse({'success': True, 'data': results}, status=200)

        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)
