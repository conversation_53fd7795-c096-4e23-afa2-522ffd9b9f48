
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent.parent

SECRET_KEY = 'django-insecure-xxx'

DEBUG = True

ALLOWED_HOSTS = ['*']

INSTALLED_APPS = [
    # Apps del núcleo de Django (obligatorias)
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',  # ✅ NECESARIA
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # Tus apps y librerías adicionales
    'corsheaders',
    'rest_framework',
    'core',  # Tu app personalizada
]


MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',

    # CORS debe estar lo más arriba posible después de seguridad
    'corsheaders.middleware.CorsMiddleware',

    'django.contrib.sessions.middleware.SessionMiddleware',  # ✅ obligatorio para sesiones y admin
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',  # ✅ para usar request.user
    'django.contrib.messages.middleware.MessageMiddleware',      # ✅ para mensajes flash

    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]



ROOT_URLCONF = 'midascrm.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'midascrm.wsgi.application'

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'midascrm_db',
        'USER': 'usuarioCrm2',
        'PASSWORD': 'Midas*2025%',
        'HOST': '************',
        'PORT': '3306',
    }
}

AUTH_PASSWORD_VALIDATORS = []

LANGUAGE_CODE = 'es'

TIME_ZONE = 'America/Lima'

USE_I18N = True

USE_TZ = True

STATIC_URL = 'static/'

CORS_ALLOW_ALL_ORIGINS = True

CORS_ALLOWED_ORIGINS = [
    "http://localhost:5200",
]
