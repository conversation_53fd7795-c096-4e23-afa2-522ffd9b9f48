from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.db import connection
import json

@method_decorator(csrf_exempt, name='dispatch')
class ListasConTareasView(View):
    def get(self, request):
        try:
            with connection.cursor() as cursor:
                # Obtener listas
                cursor.execute("""
                    SELECT id_lista, nombre, orden 
                    FROM listas_v2 
                    ORDER BY orden
                """)
                listas = cursor.fetchall()

                # Obtener tareas
                cursor.execute("""
                    SELECT 
                        id_tarea, titulo, descripcion, tipo_asignacion, asignado_a,
                        creado_por, id_lista, icono, color, estado, fecha_creacion
                    FROM tareas_v2
                    WHERE estado = 'A'
                    ORDER BY id_lista, fecha_creacion DESC
                """)
                tareas_raw = cursor.fetchall()

                # Obtener todos los usuarios y sedes en memoria
                cursor.execute("SELECT codi_usuario, CONCAT(nombre, ' ', apellido) FROM usuarios")
                usuarios_dict = dict(cursor.fetchall())

                cursor.execute("SELECT id, nombre FROM sedes")
                sedes_dict = dict(cursor.fetchall())

            from collections import defaultdict
            tareas_por_lista = defaultdict(list)

            for row in tareas_raw:
                id_tarea, titulo, descripcion, tipo_asignacion, asignado_a, creado_por, id_lista, icono, color, estado, fecha_creacion = row

                if tipo_asignacion == 'TODOS':
                    asignado_a_nombre = ['TODOS']
                elif asignado_a:
                    ids = [id.strip() for id in asignado_a.split(',') if id.strip().isdigit()]
                    if tipo_asignacion == 'SEDE':
                        asignado_a_nombre = [sedes_dict.get(int(i), f"ID {i} no encontrado") for i in ids]
                    else:  # COORDINACION o PERSONAL
                        asignado_a_nombre = [usuarios_dict.get(int(i), f"ID {i} no encontrado") for i in ids]
                else:
                    asignado_a_nombre = []

                tarea = {
                    "id_tarea": id_tarea,
                    "titulo": titulo,
                    "descripcion": descripcion,
                    "tipo_asignacion": tipo_asignacion,
                    "asignado_a": asignado_a,
                    "asignado_a_nombre": asignado_a_nombre,
                    "creado_por": creado_por,
                    "id_lista": id_lista,
                    "icono": icono,
                    "color": color,
                    "estado": estado,
                    "fecha_creacion": fecha_creacion.strftime('%Y-%m-%d %H:%M:%S')
                }

                tareas_por_lista[id_lista].append(tarea)

            resultado = []
            for lista in listas:
                resultado.append({
                    "id_lista": lista[0],
                    "nombre": lista[1],
                    "orden": lista[2],
                    "tareas": tareas_por_lista.get(lista[0], [])
                })

            return JsonResponse(resultado, safe=False)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)



@method_decorator(csrf_exempt, name='dispatch')
class CrearTareaView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)

            titulo = data.get("titulo")
            descripcion = data.get("descripcion", "")
            tipo_asignacion = data.get("tipo_asignacion")
            asignado_a = data.get("asignado_a", "")  # puede ser string con IDs separados por coma
            creado_por = data.get("creado_por")
            id_lista = data.get("id_lista")
            icono = data.get("icono", "📝")
            color = data.get("color", "#000000")

            with connection.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO tareas_v2 (
                        titulo, descripcion, tipo_asignacion, asignado_a,
                        creado_por, id_lista, icono, color, estado, fecha_creacion
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, 'A', NOW())
                """, [titulo, descripcion, tipo_asignacion, asignado_a, creado_por,
                      id_lista, icono, color])

            return JsonResponse({"mensaje": "Tarea creada exitosamente"}, status=201)

        except Exception as e:
            return JsonResponse({"error": str(e)}, status=500)



@method_decorator(csrf_exempt, name='dispatch')
class EditarTareaView(View):
    def put(self, request, id_tarea):
        try:
            data = json.loads(request.body)

            titulo = data.get("titulo")
            descripcion = data.get("descripcion", "")
            tipo_asignacion = data.get("tipo_asignacion")
            asignado_a = data.get("asignado_a", "")
            icono = data.get("icono", "📝")
            color = data.get("color", "#000000")

            with connection.cursor() as cursor:
                cursor.execute("""
                    UPDATE tareas_v2
                    SET titulo = %s, descripcion = %s, tipo_asignacion = %s,
                        asignado_a = %s, icono = %s, color = %s
                    WHERE id_tarea = %s
                """, [titulo, descripcion, tipo_asignacion, asignado_a,
                       icono, color, id_tarea])

            return JsonResponse({"mensaje": "Tarea actualizada correctamente"}, status=200)

        except Exception as e:
            return JsonResponse({"error": str(e)}, status=500)


from django.http import JsonResponse
from django.views import View
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.db import connection
from datetime import datetime


@method_decorator(csrf_exempt, name='dispatch')
class DetalleTareaView(View):
    def get(self, request, id_tarea):
        try:
            with connection.cursor() as cursor:
                # Obtener la tarea
                cursor.execute("""
                    SELECT 
                        id_tarea, titulo, descripcion, tipo_asignacion, asignado_a,
                        creado_por, icono, color, estado, fecha_creacion
                    FROM tareas_v2
                    WHERE id_tarea = %s
                """, [id_tarea])
                tarea = cursor.fetchone()

                if not tarea:
                    return JsonResponse({"error": "Tarea no encontrada"}, status=404)

                (
                    id_tarea, titulo, descripcion, tipo_asignacion, asignado_a,
                    creado_por, icono, color, estado, fecha_creacion
                ) = tarea

                # Diccionario usuarios
                cursor.execute("SELECT codi_usuario, CONCAT(nombre, ' ', apellido) FROM usuarios")
                usuarios_dict = dict(cursor.fetchall())

                # Diccionario sedes
                cursor.execute("SELECT id, nombre FROM sedes")
                sedes_dict = dict(cursor.fetchall())

                # Historial: quién hizo la tarea + su timestamp
                cursor.execute("""
                    SELECT usuario_id, fecha_movimiento 
                    FROM tareasv2_historial 
                    WHERE tarea_id = %s
                """, [id_tarea])
                historial_raw = cursor.fetchall()

                historial_dict = {
                    usuario_id: fecha_movimiento for usuario_id, fecha_movimiento in historial_raw
                }

                historial_usuarios = set(historial_dict.keys())

                # Inicializar
                asignado_ids = []
                asignado_a_nombre = []
                asignado_a_detalle = []

                ids = [int(i.strip()) for i in asignado_a.split(',') if i.strip().isdigit()]

                if tipo_asignacion == 'TODOS':
                    asignado_ids = list(usuarios_dict.keys())
                    asignado_a_nombre = [{"id": 0, "nombre": "TODOS"}]
                    asignado_a_detalle = [
                        {"id": uid, "nombre": usuarios_dict.get(uid, f"ID {uid}")}
                        for uid in asignado_ids
                    ]

                elif tipo_asignacion == 'SEDE':
                    for sede_id in ids:
                        cursor.execute("SELECT codi_usuario FROM usuarios WHERE sede_id = %s", [sede_id])
                        usuarios_sede = [r[0] for r in cursor.fetchall()]
                        asignado_ids.extend(usuarios_sede)

                        asignado_a_nombre.append({
                            "id": sede_id,
                            "nombre": sedes_dict.get(sede_id, f"Sede ID {sede_id}")
                        })

                        asignado_a_detalle.extend([
                            {"id": uid, "nombre": usuarios_dict.get(uid, f"ID {uid}")}
                            for uid in usuarios_sede
                        ])

                elif tipo_asignacion == 'COORDINACION':
                    for coord_id in ids:
                        cursor.execute("SELECT codi_usuario FROM usuarios WHERE coordinador_id = %s", [coord_id])
                        usuarios_coord = [r[0] for r in cursor.fetchall()]
                        asignado_ids.extend(usuarios_coord)

                        asignado_a_nombre.append({
                            "id": coord_id,
                            "nombre": usuarios_dict.get(coord_id, f"ID {coord_id}")
                        })

                        asignado_a_detalle.extend([
                            {"id": uid, "nombre": usuarios_dict.get(uid, f"ID {uid}")}
                            for uid in usuarios_coord
                        ])

                elif tipo_asignacion == 'PERSONAL':
                    asignado_ids.extend(ids)

                    asignado_a_nombre = [
                        {"id": uid, "nombre": usuarios_dict.get(uid, f"ID {uid}")}
                        for uid in ids
                    ]

                    asignado_a_detalle = [
                        {"id": uid, "nombre": usuarios_dict.get(uid, f"ID {uid}")}
                        for uid in ids
                    ]

                # Quitar duplicados
                asignado_ids = list(set(asignado_ids))

                # Realizados con fecha_movimiento y minutos
                realizados = []
                for uid in asignado_ids:
                    if uid in historial_dict:
                        fecha_mov = historial_dict[uid]
                        segundos_totales = int((fecha_mov - fecha_creacion).total_seconds())
                        horas = segundos_totales // 3600
                        minutos = (segundos_totales % 3600) // 60
                        segundos = segundos_totales % 60

                        # Formato legible
                        tiempo_legible = f"{horas} hora{'s' if horas != 1 else ''} {minutos} minuto{'s' if minutos != 1 else ''} {segundos} segundo{'s' if segundos != 1 else ''}"
                        realizados.append({
                            "id": uid,
                            "nombre": usuarios_dict.get(uid, f"ID {uid}"),
                            "fecha_movimiento": fecha_mov.strftime('%Y-%m-%d %H:%M:%S'),
                            "tiempo_transcurrido": tiempo_legible
                        })


                # Pendientes
                pendientes = [
                    {"id": uid, "nombre": usuarios_dict.get(uid, f"ID {uid}")}
                    for uid in asignado_ids if uid not in historial_dict
                ]

                respuesta = {
                    "id_tarea": id_tarea,
                    "titulo": titulo,
                    "descripcion": descripcion,
                    "tipo_asignacion": tipo_asignacion,
                    "asignado_a_raw": asignado_a,
                    "asignado_a_nombre": asignado_a_nombre,
                    "asignado_a_detalle": asignado_a_detalle,
                    "creado_por": usuarios_dict.get(creado_por, f"ID {creado_por}"),
                    "icono": icono,
                    "color": color,
                    "estado": estado,
                    "fecha_creacion": fecha_creacion.strftime('%Y-%m-%d %H:%M:%S'),
                    "total_asignados": len(asignado_ids),
                    "resumen_ejecucion": {
                        "hicieron_tarea": len(realizados),
                        "no_hicieron": len(pendientes)
                    },
                    "realizados": realizados,
                    "pendientes": pendientes
                }

                return JsonResponse(respuesta, json_dumps_params={'ensure_ascii': False})

        except Exception as e:
            return JsonResponse({"error": str(e)}, status=500)



from django.http import JsonResponse
from django.views import View
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.db import connection
from datetime import datetime


@method_decorator(csrf_exempt, name='dispatch')
class ListarTareasAsignadasView(View):
    def get(self, request, id_usuario):
        try:
            with connection.cursor() as cursor:
                # 1. Diccionario de usuarios
                cursor.execute("SELECT codi_usuario, CONCAT(nombre, ' ', apellido) FROM usuarios")
                usuarios_dict = dict(cursor.fetchall())

                # 2. Diccionario de sedes
                cursor.execute("SELECT id, nombre FROM sedes")
                sedes_dict = dict(cursor.fetchall())

                # 3. Obtener sede y coordinador del usuario
                cursor.execute("""
                    SELECT sede_id, coordinador_id 
                    FROM usuarios 
                    WHERE codi_usuario = %s
                """, [id_usuario])
                user_row = cursor.fetchone()
                if not user_row:
                    return JsonResponse({"error": "Usuario no encontrado"}, status=404)

                sede_id, coordinador_id = user_row

                # 4. Historial: tareas realizadas por el usuario
                cursor.execute("""
                    SELECT tarea_id, fecha_movimiento 
                    FROM tareasv2_historial 
                    WHERE usuario_id = %s
                """, [id_usuario])
                historial_usuario = {row[0]: row[1] for row in cursor.fetchall()}

                # 5. Consultar todas las tareas activas
                cursor.execute("""
                    SELECT 
                        id_tarea, titulo, descripcion, tipo_asignacion, asignado_a,
                        creado_por, id_lista, icono, color, estado, fecha_creacion
                    FROM tareas_v2
                    WHERE estado = 'A'
                """)
                tareas = cursor.fetchall()

                resultado = []
                total_realizadas = 0
                total_pendientes = 0

                for t in tareas:
                    (
                        id_tarea, titulo, descripcion, tipo_asignacion, asignado_a,
                        creado_por, id_lista, icono, color, estado, fecha_creacion
                    ) = t

                    mostrar_tarea = False
                    asignado_ids = []

                    if tipo_asignacion == 'TODOS':
                        mostrar_tarea = True
                        asignado_ids = list(usuarios_dict.keys())

                    elif tipo_asignacion == 'SEDE':
                        ids_sede = [int(i) for i in asignado_a.split(',') if i.strip().isdigit()]
                        if sede_id in ids_sede:
                            mostrar_tarea = True
                            cursor.execute("SELECT codi_usuario FROM usuarios WHERE sede_id IN %s", [tuple(ids_sede)])
                            asignado_ids = [r[0] for r in cursor.fetchall()]

                    elif tipo_asignacion == 'COORDINACION':
                        ids_coord = [int(i) for i in asignado_a.split(',') if i.strip().isdigit()]
                        if coordinador_id in ids_coord:
                            mostrar_tarea = True
                            cursor.execute("SELECT codi_usuario FROM usuarios WHERE coordinador_id IN %s", [tuple(ids_coord)])
                            asignado_ids = [r[0] for r in cursor.fetchall()]

                    elif tipo_asignacion == 'PERSONAL':
                        ids_personal = [int(i) for i in asignado_a.split(',') if i.strip().isdigit()]
                        if id_usuario in ids_personal:
                            mostrar_tarea = True
                            asignado_ids = ids_personal

                    if not mostrar_tarea:
                        continue  # Esta tarea no le corresponde al usuario

                    # Determinar si el usuario hizo la tarea
                    if id_tarea in historial_usuario:
                        total_realizadas += 1   
                        estado_usuario = 'REALIZADO'
                        fecha_mov = historial_usuario[id_tarea]
                        delta = fecha_mov - fecha_creacion
                        segundos = int(delta.total_seconds())
                        tiempo = f"{segundos//3600}h {(segundos%3600)//60}m {segundos%60}s"
                    else:
                        total_pendientes += 1
                        estado_usuario = 'PENDIENTE'
                        tiempo = None
                        fecha_mov = None

                    # Representación asignado_a
                    asignado_a_obj = []
                    ids = [int(i) for i in asignado_a.split(',') if i.strip().isdigit()]
                    if tipo_asignacion == 'SEDE':
                        for sid in ids:
                            asignado_a_obj.append({
                                "id": sid,
                                "nombre": sedes_dict.get(sid, f"Sede ID {sid}")
                            })
                    elif tipo_asignacion == 'COORDINACION':
                        for cid in ids:
                            asignado_a_obj.append({
                                "id": cid,
                                "nombre": usuarios_dict.get(cid, f"ID {cid}")
                            })
                    elif tipo_asignacion == 'PERSONAL':
                        for pid in ids:
                            asignado_a_obj.append({
                                "id": pid,
                                "nombre": usuarios_dict.get(pid, f"ID {pid}")
                            })
                    else:
                        asignado_a_obj.append({
                            "id": 0,
                            "nombre": "TODOS"
                        })

                    resultado.append({
                        "id_tarea": id_tarea,
                        "titulo": titulo,
                        "descripcion": descripcion,
                        "tipo_asignacion": tipo_asignacion,
                        "asignado_a_raw": asignado_a,
                        "asignado_a_nombre": asignado_a_obj,
                        "creado_por": usuarios_dict.get(creado_por, f"ID {creado_por}"),
                        "icono": icono,
                        "color": color,
                        "estado_tarea": estado_usuario,
                        "fecha_creacion": fecha_creacion.strftime('%Y-%m-%d %H:%M:%S'),
                        "fecha_movimiento": fecha_mov.strftime('%Y-%m-%d %H:%M:%S') if fecha_mov else None,
                        "tiempo_transcurrido": tiempo
                    })

            # Respuesta final
                resultado.sort(key=lambda x: 0 if x['estado_tarea'] == 'PENDIENTE' else 1)
                return JsonResponse({
                    "tareas": resultado,
                    "resumen": {
                        "total_tareas": total_realizadas + total_pendientes,
                        "realizadas": total_realizadas,
                        "pendientes": total_pendientes
                    }
                }, json_dumps_params={'ensure_ascii': False})

        except Exception as e:
            return JsonResponse({"error": str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class CrearHistorialTareaView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)

            tarea_id = data.get("tarea_id")
            usuario_id = data.get("usuario_id")

            if not tarea_id or not usuario_id:
                return JsonResponse({"error": "Faltan parámetros requeridos"}, status=400)

            with connection.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO tareasv2_historial (tarea_id, usuario_id, fecha_movimiento)
                    VALUES (%s, %s, NOW())
                """, [tarea_id, usuario_id])

            return JsonResponse({"mensaje": "Historial insertado correctamente"}, status=201)

        except Exception as e:
            return JsonResponse({"error": str(e)}, status=500)
        




@method_decorator(csrf_exempt, name='dispatch')
class ContarTareasAsignadasView(View):
    def get(self, request, id_usuario):
        try:
            with connection.cursor() as cursor:
                # Obtener sede y coordinador del usuario
                cursor.execute("""
                    SELECT sede_id, coordinador_id 
                    FROM usuarios 
                    WHERE codi_usuario = %s
                """, [id_usuario])
                user_row = cursor.fetchone()
                if not user_row:
                    return JsonResponse({"error": "Usuario no encontrado"}, status=404)

                sede_id, coordinador_id = user_row

                # Historial de tareas realizadas por el usuario
                cursor.execute("""
                    SELECT tarea_id 
                    FROM tareasv2_historial 
                    WHERE usuario_id = %s
                """, [id_usuario])
                historial_usuario = {row[0] for row in cursor.fetchall()}

                # Todas las tareas activas
                cursor.execute("""
                    SELECT 
                        id_tarea, tipo_asignacion, asignado_a
                    FROM tareas_v2
                    WHERE estado = 'A'
                """)
                tareas = cursor.fetchall()

                total_realizadas = 0
                total_pendientes = 0

                for id_tarea, tipo_asignacion, asignado_a in tareas:
                    mostrar_tarea = False

                    if tipo_asignacion == 'TODOS':
                        mostrar_tarea = True

                    elif tipo_asignacion == 'SEDE':
                        ids_sede = [int(i) for i in asignado_a.split(',') if i.strip().isdigit()]
                        if sede_id in ids_sede:
                            mostrar_tarea = True

                    elif tipo_asignacion == 'COORDINACION':
                        ids_coord = [int(i) for i in asignado_a.split(',') if i.strip().isdigit()]
                        if coordinador_id in ids_coord:
                            mostrar_tarea = True

                    elif tipo_asignacion == 'PERSONAL':
                        ids_personal = [int(i) for i in asignado_a.split(',') if i.strip().isdigit()]
                        if id_usuario in ids_personal:
                            mostrar_tarea = True

                    if not mostrar_tarea:
                        continue

                    if id_tarea in historial_usuario:
                        total_realizadas += 1
                    else:
                        total_pendientes += 1

                total_tareas = total_realizadas + total_pendientes
                porcentaje_avance = (
                    round((total_realizadas / total_tareas) * 100, 2)
                    if total_tareas > 0 else 0.0
                )

                return JsonResponse({
                    "total_tareas": total_tareas,
                    "realizadas": total_realizadas,
                    "pendientes": total_pendientes,
                    "porcentaje_avance": porcentaje_avance
                })

        except Exception as e:
            return JsonResponse({"error": str(e)}, status=500)





from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.db import connection

@method_decorator(csrf_exempt, name='dispatch')
class ResumenEncuestaUsuarioView(View):
    def get(self, request, id_usuario):
        try:
            with connection.cursor() as cursor:
                # Obtener sede y coordinador del usuario
                cursor.execute("SELECT sede_id, coordinador_id FROM usuarios WHERE codi_usuario = %s", [id_usuario])
                user_row = cursor.fetchone()
                if not user_row:
                    return JsonResponse({"error": "Usuario no encontrado"}, status=404)
                sede_id, coordinador_id = user_row

                encuestas_asignadas = []
                # 1. Buscar encuestas tipo TODOS
                cursor.execute("SELECT id, fecha_creacion FROM encuestas WHERE tipo_asignacion = 'TODOS' AND estado = 'A'")
                encuestas_asignadas.extend(cursor.fetchall())

                # 2. Encuestas tipo PERSONAL donde usuario_ids contiene al usuario
                cursor.execute("""
                    SELECT id, fecha_creacion FROM encuestas
                    WHERE tipo_asignacion = 'PERSONAL' AND FIND_IN_SET(%s, usuario_ids) AND estado = 'A'
                """, [id_usuario])
                encuestas_asignadas.extend(cursor.fetchall())

                # 3. Encuestas tipo COORDINACION donde el coordinador_id coincide
                if coordinador_id:
                    cursor.execute("""
                        SELECT id, fecha_creacion FROM encuestas
                        WHERE tipo_asignacion = 'COORDINACION' AND coordinador_id = %s AND estado = 'A'
                    """, [coordinador_id])
                    encuestas_asignadas.extend(cursor.fetchall())

                # 4. Encuestas tipo SEDE donde sede_id coincide
                if sede_id:
                    cursor.execute("""
                        SELECT id, fecha_creacion FROM encuestas
                        WHERE tipo_asignacion = 'SEDE' AND sede_id = %s AND estado = 'A'
                    """, [sede_id])
                    encuestas_asignadas.extend(cursor.fetchall())

                # Evitar duplicados y ordenar
                encuestas_unicas = {e[0]: e[1] for e in encuestas_asignadas}
                total_asignadas = len(encuestas_unicas)

                # Encuestas respondidas por el usuario
                cursor.execute("""
                    SELECT DISTINCT encuesta_id FROM respuestas_encuesta_usuario
                    WHERE usuario_id = %s AND completada = 1
                """, [id_usuario])
                respondidas = {row[0] for row in cursor.fetchall()}

                total_respondidas = len(respondidas.intersection(encuestas_unicas.keys()))
                porcentaje = round((total_respondidas / total_asignadas) * 100, 2) if total_asignadas else 0

                # Última encuesta por fecha
                ultima_id = None
                if encuestas_unicas:
                    ultima_id = max(encuestas_unicas, key=lambda eid: encuestas_unicas[eid])

                resumen_habilidades = {"IE": 0, "CI": 0, "CC": 0}

                if ultima_id:
                    cursor.execute("""
                        SELECT d.opcion_id, p.habilidad FROM detalles_respuesta_encuesta_usuario d
                        JOIN respuestas_encuesta_usuario r ON d.respuesta_encuesta_usuario_id = r.id
                        JOIN preguntas_encuesta p ON d.pregunta_id = p.id
                        WHERE r.usuario_id = %s AND r.encuesta_id = %s AND d.opcion_id IS NOT NULL
                    """, [id_usuario, ultima_id])
                    detalles = cursor.fetchall()

                    for opcion_id, habilidad in detalles:
                        if habilidad in resumen_habilidades:
                            cursor.execute("SELECT valor FROM opciones_respuesta_encuesta WHERE id = %s", [opcion_id])
                            valor_row = cursor.fetchone()
                            if valor_row and valor_row[0]:
                                resumen_habilidades[habilidad] += valor_row[0]

                # Respuesta final
                return JsonResponse({
                    "usuario_id": id_usuario,
                    "total_encuestas_asignadas": total_asignadas,
                    "total_encuestas_respondidas": total_respondidas,
                    "porcentaje_respondidas": porcentaje,
                    "ultima_encuesta_id": ultima_id,
                    "resumen_habilidades": resumen_habilidades
                })

        except Exception as e:
            return JsonResponse({"error": str(e)}, status=500)
        


        