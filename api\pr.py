from flask import Flask, request, jsonify
import requests
import logging

app = Flask(__name__)

# Configuración básica de logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# URL base del Catastro
BASE_URL = 'https://www1.sedecatastro.gob.es/CYCBienInmueble/OVCBusqueda.aspx'

# HEADERS comunes para las solicitudes
HEADERS = {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
}


@app.route('/api/catastro/provincias', methods=['POST'])
def get_provincias():
    try:
        data = request.get_json()
        filtro = data.get('filtro', '') if data else ''

        payload = {'filtro': filtro}
        logger.info(f'Solicitando provincias al Catastro con filtro: {filtro}')

        response = requests.post(f'{BASE_URL}/ObtenerProvincias', headers=HEADERS, json=payload)
        response.raise_for_status()

        logger.info('Respuesta del Catastro recibida correctamente')
        return jsonify(response.json()), response.status_code

    except requests.exceptions.RequestException as e:
        logger.error(f'Error al obtener provincias: {e}')
        return jsonify({'error': str(e)}), 500


@app.route('/api/catastro/municipios', methods=['POST'])
def get_municipios():
    try:
        data = request.get_json()

        if not data or 'provincia' not in data:
            return jsonify({'error': 'Se requiere el código de provincia'}), 400

        payload = {
            'provincia': data['provincia'],
            'filtro': data.get('filtro', '')
        }

        logger.info(f'Solicitando municipios al Catastro: {payload}')

        response = requests.post(f'{BASE_URL}/ObtenerMunicipios', headers=HEADERS, json=payload)
        response.raise_for_status()

        logger.info('Respuesta del Catastro recibida correctamente')
        return jsonify(response.json()), response.status_code

    except requests.exceptions.RequestException as e:
        logger.error(f'Error al obtener municipios: {e}')
        return jsonify({'error': str(e)}), 500


@app.route('/api/catastro/vias', methods=['POST'])
def get_vias():
    try:
        data = request.get_json()

        if not data or 'provincia' not in data or 'municipio' not in data:
            return jsonify({'error': 'Se requieren los códigos de provincia y municipio'}), 400

        payload = {
            'provincia': data['provincia'],
            'municipio': data['municipio'],
            'filtro': data.get('filtro', '')
        }

        logger.info(f'Solicitando vías al Catastro: {payload}')

        response = requests.post(f'{BASE_URL}/ObtenerVias', headers=HEADERS, json=payload)
        response.raise_for_status()

        logger.info('Respuesta del Catastro recibida correctamente')
        return jsonify(response.json()), response.status_code

    except requests.exceptions.RequestException as e:
        logger.error(f'Error al obtener vías: {e}')
        return jsonify({'error': str(e)}), 500


if __name__ == '__main__':
    app.run(debug=True, port=5000)
