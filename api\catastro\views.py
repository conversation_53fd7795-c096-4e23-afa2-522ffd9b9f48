from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
import requests
import logging

logger = logging.getLogger(__name__)

BASE_URL = 'https://www1.sedecatastro.gob.es/CYCBienInmueble/OVCBusqueda.aspx'
HEADERS = {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
}


class ProvinciasView(APIView):
    def post(self, request):
        filtro = request.data.get('filtro', '')
        payload = {'filtro': filtro}

        try:
            logger.info(f"Solicitando provincias con filtro: {payload}")
            res = requests.post(f"{BASE_URL}/ObtenerProvincias", headers=HEADERS, json=payload)
            res.raise_for_status()
            return Response(res.json(), status=res.status_code)
        except requests.RequestException as e:
            logger.error(f"Error al obtener provincias: {e}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ListarMunicipioView(APIView):
    def post(self, request):
        provincia = request.data.get('provincia')
        filtro = request.data.get('filtro', '')

        if not provincia:
            return Response({'error': 'Se requiere el código de provincia'}, status=status.HTTP_400_BAD_REQUEST)

        payload = {'provincia': provincia, 'filtro': filtro}

        try:
            logger.info(f"Solicitando municipios con payload: {payload}")
            res = requests.post(f"{BASE_URL}/ObtenerMunicipios", headers=HEADERS, json=payload)
            res.raise_for_status()
            return Response(res.json(), status=res.status_code)
        except requests.RequestException as e:
            logger.error(f"Error al obtener municipios: {e}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ListarViasView(APIView):
    def post(self, request):
        provincia = request.data.get('provincia')
        municipio = request.data.get('municipio')
        filtro = request.data.get('filtro', '')

        if not provincia or not municipio:
            return Response({'error': 'Se requieren los códigos de provincia y municipio'}, status=status.HTTP_400_BAD_REQUEST)

        payload = {'provincia': provincia, 'municipio': municipio, 'filtro': filtro}

        try:
            logger.info(f"Solicitando vías con payload: {payload}")
            res = requests.post(f"{BASE_URL}/ObtenerVias", headers=HEADERS, json=payload)
            res.raise_for_status()
            return Response(res.json(), status=res.status_code)
        except requests.RequestException as e:
            logger.error(f"Error al obtener vías: {e}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
