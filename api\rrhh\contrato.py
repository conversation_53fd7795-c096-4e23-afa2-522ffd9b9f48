from django.db import connection
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status

@api_view(['GET'])
def listar_contratos_por_personal(request, id_personal):
    """
    Lista todos los contratos de un personal específico
    """
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    c.id_contrato,
                    c.id_personal,
                    c.empleador_razon_social,
                    c.empleador_ruc,
                    c.empleador_domicilio,
                    c.empleador_departamento,
                    c.empleador_provincia,
                    c.empleador_distrito,
                    c.empleado_dni,
                    c.empleado_nombres,
                    c.genero,
                    c.empleado_domicilio,
                    c.empleado_departamento,
                    c.empleado_provincia,
                    c.empleado_distrito,
                    c.empleado_cargo,
                    c.empleado_cantidad_ventas,
                    c.fecha_inicio,
                    c.fecha_fin,
                    c.categoria_contrato,
                    c.sueldo,
                    c.numero_letras,
                    c.importe,
                    c.importe_letras,
                    c.fecha_firma,
                    c.estado,
                    c.created_at,
                    c.updated_at,
                    c.ruta_archivo
                FROM
                    contrato c
                WHERE
                    c.id_personal = %s AND c.estado = 'A'
                ORDER BY c.created_at DESC
            """, [id_personal])
            results = cursor.fetchall()
            data = []
            for row in results:
                data.append({
                    'id_contrato': row[0],
                    'id_personal': row[1],
                    'empleador_razon_social': row[2],
                    'empleador_ruc': row[3],
                    'empleador_domicilio': row[4],
                    'empleador_departamento': row[5],
                    'empleador_provincia': row[6],
                    'empleador_distrito': row[7],
                    'empleado_dni': row[8],
                    'empleado_nombres': row[9],
                    'genero': row[10],
                    'empleado_domicilio': row[11],
                    'empleado_departamento': row[12],
                    'empleado_provincia': row[13],
                    'empleado_distrito': row[14],
                    'empleado_cargo': row[15],
                    'empleado_cantidad_ventas': row[16],
                    'fecha_inicio': row[17],
                    'fecha_fin': row[18],
                    'categoria_contrato': row[19],
                    'sueldo': row[20],
                    'numero_letras': row[21],
                    'importe': row[22],
                    'importe_letras': row[23],
                    'fecha_firma': row[24],
                    'estado': row[25],
                    'created_at': row[26],
                    'updated_at': row[27],
                    'ruta_archivo': row[28]
                })
        return Response(data, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def obtener_contrato_por_id(request, id_contrato):
    """
    Obtiene un contrato específico por su ID
    """
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    c.*,
                    p.apellidos_nombres_completos,
                    p.documento as personal_documento
                FROM
                    contrato c
                JOIN
                    personal p ON c.id_personal = p.id_personal
                WHERE
                    c.id_contrato = %s AND c.estado = 'A'
            """, [id_contrato])
            result = cursor.fetchone()
            if result:
                data = {
                    'id_contrato': result[0],
                    'id_personal': result[1],
                    'empleador_razon_social': result[2],
                    'empleador_ruc': result[3],
                    'empleador_domicilio': result[4],
                    'empleador_departamento': result[5],
                    'empleador_provincia': result[6],
                    'empleador_distrito': result[7],
                    'empleado_dni': result[8],
                    'empleado_nombres': result[9],
                    'genero': result[10],
                    'empleado_domicilio': result[11],
                    'empleado_departamento': result[12],
                    'empleado_provincia': result[13],
                    'empleado_distrito': result[14],
                    'empleado_cargo': result[15],
                    'empleado_cantidad_ventas': result[16],
                    'fecha_inicio': result[17],
                    'fecha_fin': result[18],
                    'categoria_contrato': result[19],
                    'sueldo': result[20],
                    'numero_letras': result[21],
                    'importe': result[22],
                    'importe_letras': result[23],
                    'fecha_firma': result[24],
                    'estado': result[25],
                    'created_at': result[26],
                    'updated_at': result[27],
                    'ruta_archivo': result[28],
                    'apellidos_nombres_completos': result[29],
                    'personal_documento': result[30]
                }
                return Response(data, status=status.HTTP_200_OK)
            return Response({'mensaje': 'Contrato no encontrado'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
def crear_contrato(request):
    """
    Crea un nuevo contrato
    """
    data = request.data
    
    # Validar campos obligatorios
    campos_obligatorios = [
        'id_personal', 'empleador_razon_social', 'empleador_ruc', 'empleador_domicilio',
        'empleador_departamento', 'empleador_provincia', 'empleador_distrito',
        'empleado_dni', 'empleado_nombres', 'genero', 'empleado_domicilio',
        'empleado_departamento', 'empleado_provincia', 'empleado_distrito',
        'empleado_cargo', 'fecha_inicio', 'fecha_fin', 'categoria_contrato', 'sueldo'
    ]
    
    for campo in campos_obligatorios:
        if not data.get(campo):
            return Response({'error': f'El campo {campo} es obligatorio.'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        with connection.cursor() as cursor:
            # Lista de todos los campos
            fields = [
                'id_personal', 'empleador_razon_social', 'empleador_ruc', 'empleador_domicilio',
                'empleador_departamento', 'empleador_provincia', 'empleador_distrito',
                'empleado_dni', 'empleado_nombres', 'genero', 'empleado_domicilio',
                'empleado_departamento', 'empleado_provincia', 'empleado_distrito',
                'empleado_cargo', 'empleado_cantidad_ventas', 'fecha_inicio', 'fecha_fin',
                'categoria_contrato', 'sueldo', 'numero_letras', 'importe', 'importe_letras',
                'fecha_firma'
            ]
            
            # Procesar valores: convertir cadenas vacías a None (NULL)
            values = []
            for field in fields:
                value = data.get(field)
                if value == '':
                    values.append(None)
                else:
                    values.append(value)

            sql_query = """
                INSERT INTO contrato (
                    id_personal, empleador_razon_social, empleador_ruc, empleador_domicilio,
                    empleador_departamento, empleador_provincia, empleador_distrito,
                    empleado_dni, empleado_nombres, genero, empleado_domicilio,
                    empleado_departamento, empleado_provincia, empleado_distrito,
                    empleado_cargo, empleado_cantidad_ventas, fecha_inicio, fecha_fin,
                    categoria_contrato, sueldo, numero_letras, importe, importe_letras,
                    fecha_firma
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
            """
            
            cursor.execute(sql_query, values)
            
        return Response({'mensaje': 'Contrato creado correctamente'}, status=status.HTTP_201_CREATED)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['PUT'])
def actualizar_contrato(request, id_contrato):
    """
    Actualiza un contrato existente
    """
    data = request.data
    try:
        with connection.cursor() as cursor:
            # Lista de campos en el orden que aparecen en la consulta UPDATE
            fields = [
                'id_personal', 'empleador_razon_social', 'empleador_ruc', 'empleador_domicilio',
                'empleador_departamento', 'empleador_provincia', 'empleador_distrito',
                'empleado_dni', 'empleado_nombres', 'genero', 'empleado_domicilio',
                'empleado_departamento', 'empleado_provincia', 'empleado_distrito',
                'empleado_cargo', 'empleado_cantidad_ventas', 'fecha_inicio', 'fecha_fin',
                'categoria_contrato', 'sueldo', 'numero_letras', 'importe', 'importe_letras',
                'fecha_firma', 'ruta_archivo'
            ]

            # Procesar valores: convertir cadenas vacías a None (NULL)
            values = []
            for field in fields:
                value = data.get(field)
                if value == '':
                    values.append(None)
                else:
                    values.append(value)
            
            # Añadir el id_contrato al final para la cláusula WHERE
            values.append(id_contrato)

            sql_query = """
                UPDATE contrato SET
                    id_personal = %s, empleador_razon_social = %s, empleador_ruc = %s, empleador_domicilio = %s,
                    empleador_departamento = %s, empleador_provincia = %s, empleador_distrito = %s,
                    empleado_dni = %s, empleado_nombres = %s, genero = %s, empleado_domicilio = %s,
                    empleado_departamento = %s, empleado_provincia = %s, empleado_distrito = %s,
                    empleado_cargo = %s, empleado_cantidad_ventas = %s, fecha_inicio = %s, fecha_fin = %s,
                    categoria_contrato = %s, sueldo = %s, numero_letras = %s, importe = %s, importe_letras = %s,
                    fecha_firma = %s
                WHERE id_contrato = %s AND estado = 'A'
            """
            
            cursor.execute(sql_query, values)

        return Response({'mensaje': 'Contrato actualizado correctamente'}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['DELETE'])
def eliminar_contrato(request, id_contrato):
    """
    Elimina (inactiva) un contrato
    """
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                UPDATE contrato SET estado = 'I' WHERE id_contrato = %s
            """, [id_contrato])
            
            if cursor.rowcount == 0:
                return Response({'mensaje': 'Contrato no encontrado'}, status=status.HTTP_404_NOT_FOUND)
                
        return Response({'mensaje': 'Contrato eliminado correctamente'}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['PUT'])
def actualizar_ruta_archivo_contrato(request, id_contrato):
    """
    Actualiza solo la ruta_archivo de un contrato específico
    """
    data = request.data

    # Validar que se envíe la ruta_archivo
    if 'ruta_archivo' not in data:
        return Response({'error': 'El campo ruta_archivo es obligatorio.'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        with connection.cursor() as cursor:
            # Verificar que el contrato existe y está activo
            cursor.execute("""
                SELECT id_contrato FROM contrato
                WHERE id_contrato = %s AND estado = 'A'
            """, [id_contrato])

            if not cursor.fetchone():
                return Response({'mensaje': 'Contrato no encontrado o inactivo'}, status=status.HTTP_404_NOT_FOUND)

            # Actualizar solo la ruta_archivo
            cursor.execute("""
                UPDATE contrato
                SET ruta_archivo = %s
                WHERE id_contrato = %s AND estado = 'A'
            """, [data.get('ruta_archivo'), id_contrato])

            if cursor.rowcount == 0:
                return Response({'mensaje': 'No se pudo actualizar el contrato'}, status=status.HTTP_400_BAD_REQUEST)

        return Response({'mensaje': 'Ruta de archivo actualizada correctamente'}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
