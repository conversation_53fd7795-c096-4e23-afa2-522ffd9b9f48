from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.db import connection
import json 
@method_decorator(csrf_exempt, name='dispatch')
class RespuestasEncuestaView(View):
    def post(self, request):
        try:
            data = json.loads(request.body.decode('utf-8'))

            pregunta_id = data.get('pregunta_id')
            opcion_id = data.get('opcion_id')
            tipo_pregunta = data.get('tipo_pregunta')

            if not (pregunta_id and opcion_id and tipo_pregunta):
                return JsonResponse({
                    'success': False,
                    'error': 'Debe enviar pregunta_id, opcion_id y tipo_pregunta en el cuerpo JSON.'
                }, status=400)

            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT 
                      o.pregunta_id,
                      p.enunciado AS enunciado_pregunta,
                      p.tipo AS tipo_pregunta,
                      o.id AS opcion_id,
                      o.texto AS texto_opcion,
                      o.valor,
                      COUNT(dru.id) AS cantidad_marcada
                    FROM 
                      opciones_respuesta_encuesta o
                    INNER JOIN 
                      preguntas_encuesta p ON p.id = o.pregunta_id
                    LEFT JOIN 
                      detalles_respuesta_encuesta_usuario dru 
                        ON dru.opcion_id = o.id
                    WHERE 
                      o.pregunta_id = %s
                      AND o.id = %s
                      AND p.tipo = %s
                    GROUP BY 
                      o.pregunta_id, p.enunciado, p.tipo, o.id, o.texto
                    ORDER BY 
                      o.id
                """, [pregunta_id, opcion_id, tipo_pregunta])

                columns = [col[0] for col in cursor.description]
                results = [dict(zip(columns, row)) for row in cursor.fetchall()]

            return JsonResponse({'success': True, 'data': results}, status=200)

        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class RespuestasTextoLibreView(View):
    def post(self, request):
        try:
            data = json.loads(request.body.decode('utf-8'))
            pregunta_id = data.get('pregunta_id')

            if not pregunta_id:
                return JsonResponse({
                    'success': False,
                    'error': 'Debe enviar pregunta_id en el cuerpo JSON.'
                }, status=400)

            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT 
                      p.id AS pregunta_id,
                      p.enunciado AS enunciado_pregunta,
                      p.tipo AS tipo_pregunta,
                      NULL AS opcion_id,
                      NULL AS texto_opcion,
                      NULL AS cantidad_marcada,
                      dru.respuesta_texto AS respuesta_libre
                    FROM 
                      preguntas_encuesta p
                    INNER JOIN 
                      detalles_respuesta_encuesta_usuario dru 
                      ON dru.pregunta_id = p.id
                    WHERE 
                      p.id = %s
                      AND p.tipo = 'TEXTO_LIBRE'
                """, [pregunta_id])

                columns = [col[0] for col in cursor.description]
                results = [dict(zip(columns, row)) for row in cursor.fetchall()]

            return JsonResponse({'success': True, 'data': results}, status=200)

        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)
        
from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.db import connection
import json

@method_decorator(csrf_exempt, name='dispatch')
class EstadisticasEncuestaView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            encuesta_id = int(data.get("encuesta_id"))

            if not encuesta_id:
                return JsonResponse({"error": "encuesta_id es obligatorio"}, status=400)

            with connection.cursor() as cursor:
                # === Obtener metadata ===
                cursor.execute("""
                    SELECT id, titulo, descripcion, fecha_creacion, fecha_fin, tipo_asignacion,
                           usuario_ids, sede_id, coordinador_id
                    FROM encuestas WHERE id = %s
                """, [encuesta_id])
                row = cursor.fetchone()
                if not row:
                    return JsonResponse({"error": "Encuesta no encontrada"}, status=404)

                (id_, titulo, descripcion, fecha_creacion, fecha_fin,
                 tipo_asignacion, usuario_ids, sede_id, coordinador_id) = row

                # === Determinar asignación ===
                asignado_a = ""
                total_asignados = 0

                if tipo_asignacion == "TODOS":
                    asignado_a = ["Todos los usuarios"]
                    cursor.execute("SELECT COUNT(*) FROM usuarios WHERE estado = 'A'")
                    total_asignados = cursor.fetchone()[0]

                elif tipo_asignacion == "PERSONAL" and usuario_ids:
                    ids = [int(i.strip()) for i in usuario_ids.split(",") if i.strip().isdigit()]
                    total_asignados = len(ids)
                    if ids:
                        placeholders = ",".join(["%s"] * len(ids))
                        cursor.execute(f"""
                            SELECT CONCAT(nombre, ' ', apellido) FROM usuarios
                            WHERE codi_usuario IN ({placeholders})
                        """, ids)
                        nombres = [r[0] for r in cursor.fetchall()]
                        asignado_a = nombres

                elif tipo_asignacion == "SEDE" and sede_id:
                    cursor.execute("SELECT nombre FROM sedes WHERE id = %s", [sede_id])
                    sede_row = cursor.fetchone()
                    asignado_a = [sede_row[0]] if sede_row else ["Sede desconocida"]
                    cursor.execute("SELECT COUNT(*) FROM usuarios WHERE sede_id = %s", [sede_id])
                    total_asignados = cursor.fetchone()[0]

                elif tipo_asignacion == "COORDINACION" and coordinador_id:
                    cursor.execute("SELECT CONCAT(nombre, ' ', apellido) FROM usuarios WHERE codi_usuario = %s", [coordinador_id])
                    coord_row = cursor.fetchone()
                    asignado_a = [coord_row[0]] if coord_row else ["Coordinador desconocido"]
                    cursor.execute("SELECT COUNT(*) FROM usuarios WHERE coordinador_id = %s", [coordinador_id])
                    total_asignados = cursor.fetchone()[0]

                # === Total de respuestas ===
                cursor.execute("""
                    SELECT COUNT(*) FROM respuestas_encuesta_usuario
                    WHERE encuesta_id = %s AND completada = 1
                """, [encuesta_id])
                total_respondieron = cursor.fetchone()[0]
                porcentaje_respondieron = round((total_respondieron / total_asignados) * 100, 2) if total_asignados else 0

                # === Preguntas ===
                cursor.execute("""
                    SELECT id, enunciado, tipo, habilidad FROM preguntas_encuesta
                    WHERE encuesta_id = %s ORDER BY orden
                """, [encuesta_id])
                preguntas = cursor.fetchall()

                preguntas_json = []

                for pregunta_id, enunciado, tipo, habilidad in preguntas:
                    # Total respuestas reales por pregunta y encuesta
                    cursor.execute("""
                        SELECT COUNT(*) FROM detalles_respuesta_encuesta_usuario d
                        JOIN respuestas_encuesta_usuario r ON r.id = d.respuesta_encuesta_usuario_id
                        WHERE d.pregunta_id = %s AND r.encuesta_id = %s
                    """, [pregunta_id, encuesta_id])
                    total_respuestas = cursor.fetchone()[0]

                    pregunta_json = {
                        "id": pregunta_id,
                        "enunciado": enunciado,
                        "tipo": tipo,
                        "habilidad": habilidad,
                        "total_respuestas": total_respuestas,
                        "sin_respuestas": total_respuestas == 0,
                        "opciones": [],
                        "respuestas_texto": [],
                        "respuestas_numero": [],
                        "respuestas_fecha": []
                    }

                    if tipo in ("OPCION_MULTIPLE", "SELECCION_MULTIPLE", "ESCALA_LIKERT"):
                          cursor.execute("""
                              SELECT
                                  o.id,
                                  o.texto,
                                  o.valor,
                                  COUNT(d.id) as cantidad
                              FROM opciones_respuesta_encuesta o
                              LEFT JOIN detalles_respuesta_encuesta_usuario d ON d.opcion_id = o.id
                              LEFT JOIN respuestas_encuesta_usuario r ON r.id = d.respuesta_encuesta_usuario_id
                              WHERE o.pregunta_id = %s AND r.encuesta_id = %s
                              GROUP BY o.id, o.texto
                          """, [pregunta_id, encuesta_id])
                          
                          opciones = cursor.fetchall()
                          
                          for opcion_id, texto, valor, cantidad in opciones:
                              porcentaje = (cantidad / total_respuestas) * 100 if total_respuestas else 0

                              # Usuarios que marcaron esta opción
                              cursor.execute("""
                                  SELECT CONCAT(u.nombre, ' ', u.apellido)
                                  FROM detalles_respuesta_encuesta_usuario d
                                  JOIN respuestas_encuesta_usuario r ON r.id = d.respuesta_encuesta_usuario_id
                                  JOIN usuarios u ON r.usuario_id = u.codi_usuario
                                  WHERE d.opcion_id = %s AND r.encuesta_id = %s
                              """, [opcion_id, encuesta_id])
                              usuarios = [row[0] for row in cursor.fetchall()]

                              pregunta_json["opciones"].append({
                                  "texto": texto,
                                  "valor": valor,
                                  "cantidad": cantidad,
                                  "porcentaje": round(porcentaje, 2),
                                  "usuarios": usuarios
                              })

                          for texto, cantidad in cursor.fetchall():
                              porcentaje = (cantidad / total_respuestas) * 100 if total_respuestas else 0
                              pregunta_json["opciones"].append({
                                  "texto": texto,
                                  "cantidad": cantidad,
                                  "porcentaje": round(porcentaje, 2)
                              })

                    elif tipo == "TEXTO_LIBRE":
                      cursor.execute("""
                          SELECT CONCAT(u.nombre, ' ', u.apellido) AS nombre_completo, d.respuesta_texto
                          FROM detalles_respuesta_encuesta_usuario d
                          JOIN respuestas_encuesta_usuario r ON r.id = d.respuesta_encuesta_usuario_id
                          JOIN usuarios u ON r.usuario_id = u.codi_usuario
                          WHERE d.pregunta_id = %s AND r.encuesta_id = %s AND d.respuesta_texto IS NOT NULL
                      """, [pregunta_id, encuesta_id])
                      
                      respuestas = []
                      for nombre, respuesta in cursor.fetchall():
                          respuestas.append({
                              "nombre": nombre,
                              "respuesta": respuesta
                          })
                      pregunta_json["respuestas_texto"] = respuestas



                    elif tipo == "NUMERO":
                        cursor.execute("""
                            SELECT d.respuesta_numero
                            FROM detalles_respuesta_encuesta_usuario d
                            JOIN respuestas_encuesta_usuario r ON r.id = d.respuesta_encuesta_usuario_id
                            WHERE d.pregunta_id = %s AND r.encuesta_id = %s AND d.respuesta_numero IS NOT NULL
                        """, [pregunta_id, encuesta_id])
                        pregunta_json["respuestas_numero"] = [r[0] for r in cursor.fetchall()]

                    elif tipo == "FECHA":
                        cursor.execute("""
                            SELECT d.respuesta_fecha
                            FROM detalles_respuesta_encuesta_usuario d
                            JOIN respuestas_encuesta_usuario r ON r.id = d.respuesta_encuesta_usuario_id
                            WHERE d.pregunta_id = %s AND r.encuesta_id = %s AND d.respuesta_fecha IS NOT NULL
                        """, [pregunta_id, encuesta_id])
                        pregunta_json["respuestas_fecha"] = [str(r[0]) for r in cursor.fetchall()]

                    preguntas_json.append(pregunta_json)

                result = {
                    "encuesta_id": encuesta_id,
                    "titulo": titulo,
                    "descripcion": descripcion,
                    "fecha_creacion": str(fecha_creacion),
                    "fecha_fin": str(fecha_fin),
                    "tipo_asignacion": tipo_asignacion,
                    "asignado_a": asignado_a,
                    "total_asignados": total_asignados,
                    "total_respondieron": total_respondieron,
                    "porcentaje_respondieron": porcentaje_respondieron,
                    "preguntas": preguntas_json
                }

                print(f"Resultado generado para encuesta {encuesta_id}: {titulo}")
                
                return JsonResponse(result, status=200, safe=False)

        except Exception as e:
            return JsonResponse({"error": str(e)}, status=500)
