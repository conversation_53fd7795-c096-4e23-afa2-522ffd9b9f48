from django.db import connection
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status

@api_view(['GET'])
def listar_bonos_por_personal(request, id_personal):
    """
    Lista todos los bonos de un personal específico
    """
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    b.id_bono,
                    b.id_personal,
                    b.tipo_bono,
                    b.descripcion,
                    b.monto,
                    b.fecha_otorgamiento,
                    b.periodo,
                    b.anio,
                    b.mes,
                    b.estado,
                    b.observaciones,
                    b.created_at,
                    b.updated_at,
                    p.apellidos_nombres_completos,
                    p.documento as personal_documento,
                    p.cargo
                FROM
                    bonos b
                JOIN
                    personal p ON b.id_personal = p.id_personal
                WHERE
                    b.id_personal = %s
                ORDER BY b.fecha_otorgamiento DESC, b.created_at DESC
            """, [id_personal])
            results = cursor.fetchall()
            data = []
            for row in results:
                data.append({
                    'id_bono': row[0],
                    'id_personal': row[1],
                    'tipo_bono': row[2],
                    'descripcion': row[3],
                    'monto': row[4],
                    'fecha_otorgamiento': row[5],
                    'periodo': row[6],
                    'anio': row[7],
                    'mes': row[8],
                    'estado': row[9],
                    'observaciones': row[10],
                    'created_at': row[11],
                    'updated_at': row[12],
                    'apellidos_nombres_completos': row[13],
                    'personal_documento': row[14],
                    'cargo': row[15]
                })
        return Response(data, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def obtener_bono_por_id(request, id_bono):
    """
    Obtiene un bono específico por su ID
    """
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    b.*,
                    p.apellidos_nombres_completos,
                    p.documento as personal_documento,
                    p.cargo,
                    p.sede
                FROM
                    bonos b
                JOIN
                    personal p ON b.id_personal = p.id_personal
                WHERE
                    b.id_bono = %s
            """, [id_bono])
            result = cursor.fetchone()
            if result:
                data = {
                    'id_bono': result[0],
                    'id_personal': result[1],
                    'tipo_bono': result[2],
                    'descripcion': result[3],
                    'monto': result[4],
                    'fecha_otorgamiento': result[5],
                    'periodo': result[6],
                    'anio': result[7],
                    'mes': result[8],
                    'estado': result[9],
                    'observaciones': result[10],
                    'created_at': result[11],
                    'updated_at': result[12],
                    'apellidos_nombres_completos': result[13],
                    'personal_documento': result[14],
                    'cargo': result[15],
                    'sede': result[16]
                }
                return Response(data, status=status.HTTP_200_OK)
            return Response({'mensaje': 'Bono no encontrado'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
def crear_bono(request):
    """
    Crea un nuevo bono
    """
    data = request.data
    
    # Validar campos obligatorios
    campos_obligatorios = ['id_personal', 'tipo_bono', 'monto', 'fecha_otorgamiento']
    
    for campo in campos_obligatorios:
        if not data.get(campo):
            return Response({'error': f'El campo {campo} es obligatorio.'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        with connection.cursor() as cursor:
            # Lista de todos los campos
            fields = [
                'id_personal', 'tipo_bono', 'descripcion', 'monto', 'fecha_otorgamiento',
                'periodo', 'anio', 'mes', 'estado', 'observaciones'
            ]
            
            # Procesar valores: convertir cadenas vacías a None (NULL)
            values = []
            for field in fields:
                value = data.get(field)
                if value == '':
                    values.append(None)
                else:
                    values.append(value)

            sql_query = """
                INSERT INTO bonos (
                    id_personal, tipo_bono, descripcion, monto, fecha_otorgamiento,
                    periodo, anio, mes, estado, observaciones
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
            """
            
            cursor.execute(sql_query, values)
            
        return Response({'mensaje': 'Bono creado correctamente'}, status=status.HTTP_201_CREATED)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['PUT'])
def actualizar_bono(request, id_bono):
    """
    Actualiza un bono existente
    """
    data = request.data
    try:
        with connection.cursor() as cursor:
            # Lista de campos en el orden que aparecen en la consulta UPDATE
            fields = [
                'id_personal', 'tipo_bono', 'descripcion', 'monto', 'fecha_otorgamiento',
                'periodo', 'anio', 'mes', 'estado', 'observaciones'
            ]

            # Procesar valores: convertir cadenas vacías a None (NULL)
            values = []
            for field in fields:
                value = data.get(field)
                if value == '':
                    values.append(None)
                else:
                    values.append(value)
            
            # Añadir el id_bono al final para la cláusula WHERE
            values.append(id_bono)

            sql_query = """
                UPDATE bonos SET
                    id_personal = %s, tipo_bono = %s, descripcion = %s, monto = %s, fecha_otorgamiento = %s,
                    periodo = %s, anio = %s, mes = %s, estado = %s, observaciones = %s
                WHERE id_bono = %s
            """
            
            cursor.execute(sql_query, values)

        return Response({'mensaje': 'Bono actualizado correctamente'}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['DELETE'])
def eliminar_bono(request, id_bono):
    """
    Elimina un bono
    """
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                DELETE FROM bonos WHERE id_bono = %s
            """, [id_bono])
            
            if cursor.rowcount == 0:
                return Response({'mensaje': 'Bono no encontrado'}, status=status.HTTP_404_NOT_FOUND)
                
        return Response({'mensaje': 'Bono eliminado correctamente'}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def resumen_bonos_personal(request, id_personal, anio):
    """
    Obtiene un resumen de bonos por personal y año
    """
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    COUNT(*) as total_bonos,
                    SUM(monto) as monto_total,
                    AVG(monto) as monto_promedio,
                    MIN(monto) as monto_minimo,
                    MAX(monto) as monto_maximo,
                    p.apellidos_nombres_completos,
                    p.documento,
                    p.cargo
                FROM
                    bonos b
                JOIN
                    personal p ON b.id_personal = p.id_personal
                WHERE
                    b.id_personal = %s AND b.anio = %s
                GROUP BY b.id_personal, p.apellidos_nombres_completos, p.documento, p.cargo
            """, [id_personal, anio])
            result = cursor.fetchone()
            
            if result:
                data = {
                    'id_personal': id_personal,
                    'anio': anio,
                    'total_bonos': result[0] or 0,
                    'monto_total': result[1] or 0,
                    'monto_promedio': result[2] or 0,
                    'monto_minimo': result[3] or 0,
                    'monto_maximo': result[4] or 0,
                    'apellidos_nombres_completos': result[5],
                    'documento': result[6],
                    'cargo': result[7]
                }
                return Response(data, status=status.HTTP_200_OK)
            else:
                return Response({
                    'id_personal': id_personal,
                    'anio': anio,
                    'total_bonos': 0,
                    'monto_total': 0,
                    'monto_promedio': 0,
                    'monto_minimo': 0,
                    'monto_maximo': 0,
                    'mensaje': 'No se encontraron bonos para este personal y año'
                }, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def listar_bonos_por_personal_fechas(request, id_personal, fecha_inicio, fecha_fin):
    """
    Lista todos los bonos de un personal específico entre fechas de pago
    """
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    b.id_bono,
                    b.id_personal,
                    b.tipo_bono,
                    b.descripcion,
                    b.monto,
                    b.fecha_otorgamiento,
                    b.periodo,
                    b.anio,
                    b.mes,
                    b.estado,
                    b.observaciones,
                    b.created_at,
                    b.updated_at,
                    p.apellidos_nombres_completos,
                    p.documento as personal_documento,
                    p.cargo,
                    p.sede
                FROM
                    bonos b
                JOIN
                    personal p ON b.id_personal = p.id_personal
                WHERE
                    b.id_personal = %s
                    AND b.fecha_otorgamiento BETWEEN %s AND %s
                ORDER BY b.fecha_otorgamiento DESC, b.created_at DESC
            """, [id_personal, fecha_inicio, fecha_fin])
            results = cursor.fetchall()
            data = []
            total_monto = 0

            for row in results:
                monto = float(row[4]) if row[4] else 0
                total_monto += monto

                data.append({
                    'id_bono': row[0],
                    'id_personal': row[1],
                    'tipo_bono': row[2],
                    'descripcion': row[3],
                    'monto': monto,
                    'fecha_otorgamiento': row[5],
                    'periodo': row[6],
                    'anio': row[7],
                    'mes': row[8],
                    'estado': row[9],
                    'observaciones': row[10],
                    'created_at': row[11],
                    'updated_at': row[12],
                    'apellidos_nombres_completos': row[13],
                    'personal_documento': row[14],
                    'cargo': row[15],
                    'sede': row[16]
                })

        return Response({
            'id_personal': id_personal,
            'fecha_inicio': fecha_inicio,
            'fecha_fin': fecha_fin,
            'total_bonos': len(data),
            'monto_total': total_monto,
            'bonos': data
        }, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
