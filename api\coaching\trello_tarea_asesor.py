from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.db import connection
import json
from datetime import datetime
import pytz

@method_decorator(csrf_exempt, name='dispatch')
class ListarListasConTareasView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            sede_id = data.get('sede_id')
            coordinador_id = data.get('coordinador_id')
            user_id = data.get('user_id')

            if not user_id:
                return JsonResponse({'error': 'user_id es obligatorio'}, status=400)

            # 1. Obtener el id_tablero de alguna tarea por hacer
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT DISTINCT t.id_tablero
                    FROM tareas t
                    JOIN listas l ON t.id_lista = l.id_lista
                    WHERE LOWER(l.estado) IN ('por hacer', 'hoy', 'pendientes', 'pendiente')
                      AND t.estado = 'A'
                      AND (
                        t.tipo_asignacion = 'TODOS'
                        OR (t.tipo_asignacion = 'SEDE' AND t.asignado_a = %s)
                        OR (t.tipo_asignacion = 'COORDINACION' AND t.asignado_a = %s)
                        OR (t.tipo_asignacion = 'PERSONAL' AND FIND_IN_SET(%s, t.asignado_a))
                      )
                    LIMIT 1
                """, [str(sede_id), str(coordinador_id), str(user_id)])
                row = cursor.fetchone()
                if not row:
                    return JsonResponse({'listas': []}, status=200)
                id_tablero = row[0]

            # 2. Obtener las listas del tablero
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT id_lista, nombre, estado, orden
                    FROM listas
                    WHERE id_tablero = %s AND estado_vista = 'A'
                    ORDER BY orden
                """, [id_tablero])
                listas_raw = cursor.fetchall()
                listas = []
                for lista in listas_raw:
                    listas.append({
                        'id_lista': lista[0],
                        'nombre': lista[1],
                        'estado': lista[2],
                        'orden': lista[3],
                        'tareas': []
                    })

            # 3. Obtener las tareas por hacer del usuario
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT t.*, l.id_lista
                    FROM tareas t
                    JOIN listas l ON t.id_lista = l.id_lista
                    WHERE l.id_tablero = %s
                      AND t.estado = 'A'
                      AND (
                        t.tipo_asignacion = 'TODOS'
                        OR (t.tipo_asignacion = 'SEDE' AND t.asignado_a = %s)
                        OR (t.tipo_asignacion = 'COORDINACION' AND t.asignado_a = %s)
                        OR (t.tipo_asignacion = 'PERSONAL' AND FIND_IN_SET(%s, t.asignado_a))
                      )
                """, [id_tablero, str(sede_id), str(coordinador_id), str(user_id)])
                tareas_raw = cursor.fetchall()
                columnas = [col[0] for col in cursor.description]
                tareas = [dict(zip(columnas, fila)) for fila in tareas_raw]

            # 4. Anidar tareas en sus respectivas listas
            for tarea in tareas:
                for lista in listas:
                    if lista['id_lista'] == tarea['id_lista']:
                        lista['tareas'].append(tarea)

            return JsonResponse({'listas': listas}, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
        

@method_decorator(csrf_exempt, name='dispatch')
class RegistrarMovimientoTareaView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)

            id_tarea = data.get('id_tarea')
            id_usuario = data.get('id_usuario')
            id_lista = data.get('id_lista')
            estado_anterior = data.get('estado_anterior')
            estado_nuevo = data.get('estado_nuevo')

            if not all([id_tarea, id_usuario, id_lista, estado_anterior, estado_nuevo]):
                return JsonResponse({
                    "estado": 400,
                    "mensaje": "Faltan campos obligatorios.",
                    "datos": None
                })

            # Obtener fecha y hora actual en Lima
            lima_tz = pytz.timezone('America/Lima')
            fecha_movimiento = datetime.now(lima_tz)

            with connection.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO tarea_usuario_estado (
                        id_tarea, id_usuario, id_lista, estado_anterior, estado_nuevo, fecha_movimiento
                    )
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, [id_tarea, id_usuario, id_lista, estado_anterior, estado_nuevo, fecha_movimiento])

            return JsonResponse({
                "estado": 200,
                "mensaje": "Movimiento registrado exitosamente.",
                "datos": True
            })

        except Exception as e:
            return JsonResponse({
                "estado": 500,
                "mensaje": str(e),
                "datos": None
            })
        

@method_decorator(csrf_exempt, name='dispatch')
class ListarTareasPorUsuarioView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            id_usuario = data.get('id_usuario')

            if not id_usuario:
                return JsonResponse({
                    "estado": 400,
                    "mensaje": "El campo id_usuario es obligatorio.",
                    "datos": None
                })

            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT 
                        t.id_tarea,
                        t.titulo,
                        l.nombre AS lista_actual,
                        te.estado_anterior,
                        te.estado_nuevo,
                        te.fecha_movimiento
                    FROM tarea_usuario_estado te
                    JOIN tareas t ON te.id_tarea = t.id_tarea
                    JOIN listas l ON te.id_lista = l.id_lista
                    WHERE te.id_usuario = %s
                    ORDER BY te.fecha_movimiento DESC;
                """, [id_usuario])

                filas = cursor.fetchall()
                columnas = [col[0] for col in cursor.description]
                datos = [dict(zip(columnas, fila)) for fila in filas]

            return JsonResponse({
                "estado": 200,
                "mensaje": "Tareas encontradas.",
                "datos": datos
            })

        except Exception as e:
            return JsonResponse({
                "estado": 500,
                "mensaje": str(e),
                "datos": None
            })


@method_decorator(csrf_exempt, name='dispatch')
class ListarListasYMovimientosView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            sede_id = data.get('sede_id')
            coordinador_id = data.get('coordinador_id')
            user_id = data.get('user_id')

            if not user_id:
                return JsonResponse({'error': 'user_id es obligatorio'}, status=400)

            # 1. Obtener id_tablero asociado al usuario
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT DISTINCT t.id_tablero
                    FROM tareas t
                    JOIN listas l ON t.id_lista = l.id_lista
                    WHERE LOWER(l.estado) IN ('por hacer', 'hoy', 'pendientes', 'pendiente')
                      AND t.estado = 'A'
                      AND (
                        t.tipo_asignacion = 'TODOS'
                        OR (t.tipo_asignacion = 'SEDE' AND t.asignado_a = %s)
                        OR (t.tipo_asignacion = 'COORDINACION' AND t.asignado_a = %s)
                        OR (t.tipo_asignacion = 'PERSONAL' AND FIND_IN_SET(%s, t.asignado_a))
                      )
                    LIMIT 1
                """, [str(sede_id), str(coordinador_id), str(user_id)])
                row = cursor.fetchone()
                if not row:
                    return JsonResponse({'listas': []}, status=200)
                id_tablero = row[0]

            # 2. Obtener listas del tablero
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT id_lista, nombre, estado, orden
                    FROM listas
                    WHERE id_tablero = %s AND estado_vista = 'A'
                    ORDER BY orden
                """, [id_tablero])
                listas_raw = cursor.fetchall()
                listas = [{
                    'id_lista': lista[0],
                    'nombre': lista[1],
                    'estado': lista[2],
                    'orden': lista[3],
                    'tareas': []
                } for lista in listas_raw]

            # 3. Mapear listas por ID y por nombre
            mapa_listas = {l['id_lista']: l for l in listas}
            mapa_listas_nombre = {l['nombre'].strip().lower(): l for l in listas}

            # 4. Obtener tareas visibles al usuario
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT t.*, l.id_lista
                    FROM tareas t
                    JOIN listas l ON t.id_lista = l.id_lista
                    WHERE l.id_tablero = %s
                      AND t.estado = 'A'
                      AND (
                        t.tipo_asignacion = 'TODOS'
                        OR (t.tipo_asignacion = 'SEDE' AND t.asignado_a = %s)
                        OR (t.tipo_asignacion = 'COORDINACION' AND t.asignado_a = %s)
                        OR (t.tipo_asignacion = 'PERSONAL' AND FIND_IN_SET(%s, t.asignado_a))
                      )
                """, [id_tablero, str(sede_id), str(coordinador_id), str(user_id)])
                columnas = [col[0] for col in cursor.description]
                tareas = [dict(zip(columnas, fila)) for fila in cursor.fetchall()]

            # 5. Obtener últimos movimientos del usuario
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT DISTINCT te.id_tarea,
                        te.estado_nuevo,
                        te.id_lista,
                        l.nombre AS nombre_lista
                    FROM tarea_usuario_estado te
                    JOIN listas l ON te.id_lista = l.id_lista
                    WHERE te.id_usuario = %s
                    AND te.fecha_movimiento = (
                        SELECT MAX(fecha_movimiento)
                        FROM tarea_usuario_estado
                        WHERE id_tarea = te.id_tarea AND id_usuario = te.id_usuario
                    )
                """, [user_id])
                movimientos = cursor.fetchall()
                movimientos_dict = {
                    row[0]: {
                        'estado_nuevo': row[1],
                        'id_lista': row[2],
                        'nombre_lista': row[3]
                    } for row in movimientos
                }

            # 6. Distribuir tareas en listas según movimiento
            for tarea in tareas:
                id_tarea = tarea['id_tarea']
                if id_tarea in movimientos_dict:
                    id_lista_mov = movimientos_dict[id_tarea]['id_lista']
                    if id_lista_mov in mapa_listas:
                        mapa_listas[id_lista_mov]['tareas'].append(tarea)
                else:
                    # Si no hay movimiento, usar lista original
                    id_lista_original = tarea['id_lista']
                    if id_lista_original in mapa_listas:
                        mapa_listas[id_lista_original]['tareas'].append(tarea)

            return JsonResponse({'listas': listas}, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
