# trello_backend/api/views/tableros_view.py

from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.db import connection
import json

@method_decorator(csrf_exempt, name='dispatch')
class TableroCreateView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            nombre = data.get('nombre')
            descripcion = data.get('descripcion')
            creado_por = data.get('creado_por')  # id del usuario
            color = data.get('color')  # color opcional, por defecto 'default'

            if not all([nombre, creado_por]):
                return JsonResponse({'error': 'Nombre y creado_por son obligatorios'}, status=400)

            with connection.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO tableros (nombre, descripcion, creado_por, color)
                    VALUES (%s, %s, %s, %s)
                """, [nombre, descripcion, creado_por, color])

            return JsonResponse({'mensaje': 'Tablero creado correctamente'}, status=201)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class TableroListView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            usuario_id = data.get('usuario_id')
            rol = data.get('rol')

            if not all([usuario_id, rol]):
                return JsonResponse({'error': 'usuario_id y rol son obligatorios'}, status=400)

            with connection.cursor() as cursor:
                if rol in ['ADMIN', 'COACHING', 'PROGRAMADOR']:
                    cursor.execute("SELECT id_tablero, nombre, descripcion FROM tableros WHERE estado = 'A'")
                elif rol == 'COORDINADOR':
                    cursor.execute("""
                        SELECT t.id_tablero, t.nombre, t.descripcion, t.color
                        FROM tableros t
                        JOIN miembros_tablero mt ON t.id_tablero = mt.id_tablero
                        WHERE t.estado = 'A' AND (mt.id_usuario = %s OR mt.id_usuario IN (
                            SELECT codi_usuario FROM usuarios WHERE coordinador_id = %s
                        ))
                    """, [usuario_id, usuario_id])
                else:
                    cursor.execute("""
                        SELECT t.id_tablero, t.nombre, t.descripcion, t.color
                        FROM tableros t
                        JOIN miembros_tablero mt ON t.id_tablero = mt.id_tablero
                        WHERE t.estado = 'A' AND mt.id_usuario = %s
                    """, [usuario_id])

                resultados = cursor.fetchall()
                tableros = [{'id': row[0], 'nombre': row[1], 'descripcion': row[2]} for row in resultados]

            return JsonResponse({'tableros': tableros}, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
        
@method_decorator(csrf_exempt, name='dispatch')
class TableroListIdView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            usuario_id = data.get('usuario_id')

            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT t.id_tablero, t.nombre, t.descripcion, t.color
                    FROM tableros t
                    WHERE t.estado = 'A' AND t.creado_por = %s
                """, [usuario_id])

                resultados = cursor.fetchall()
                tableros = [{'id': row[0], 'nombre': row[1], 'descripcion': row[2], 'color':row[3]} for row in resultados]

            return JsonResponse({'tableros': tableros}, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)



@method_decorator(csrf_exempt, name='dispatch')
class TableroUpdateView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            id_tablero = data.get('id_tablero')
            nuevo_nombre = data.get('nombre')
            nueva_descripcion = data.get('descripcion')
            usuario_id = data.get('usuario_id')
            rol = data.get('rol')
            color = data.get('color') 

            if not all([id_tablero, usuario_id, rol]):
                return JsonResponse({'error': 'id_tablero, usuario_id y rol son obligatorios'}, status=400)

            with connection.cursor() as cursor:
                cursor.execute("SELECT creado_por FROM tableros WHERE id_tablero = %s", [id_tablero])
                resultado = cursor.fetchone()
                if not resultado:
                    return JsonResponse({'error': 'Tablero no encontrado'}, status=404)

                if resultado[0] != usuario_id and rol not in ['ADMIN', 'PROGRAMADOR']:
                    return JsonResponse({'error': 'No autorizado para editar este tablero'}, status=403)

                cursor.execute("""
                    UPDATE tableros SET nombre = %s, descripcion = %s, color = %s
                    WHERE id_tablero = %s
                """, [nuevo_nombre, nueva_descripcion, color, id_tablero])

            return JsonResponse({'mensaje': 'Tablero actualizado correctamente'}, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class TableroInactivarView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            id_tablero = data.get('id_tablero')
            usuario_id = data.get('usuario_id')
            rol = data.get('rol')

            if not all([id_tablero, usuario_id, rol]):
                return JsonResponse({'error': 'id_tablero, usuario_id y rol son obligatorios'}, status=400)

            with connection.cursor() as cursor:
                # Verificamos si el tablero existe y quién lo creó
                cursor.execute("SELECT creado_por FROM tableros WHERE id_tablero = %s", [id_tablero])
                resultado = cursor.fetchone()
                if not resultado:
                    return JsonResponse({'error': 'Tablero no encontrado'}, status=404)

                if resultado[0] != usuario_id and rol not in ['ADMIN', 'PROGRAMADOR']:
                    return JsonResponse({'error': 'No autorizado para inactivar este tablero'}, status=403)

                # Inactivar el tablero
                cursor.execute("UPDATE tableros SET estado = 'I' WHERE id_tablero = %s", [id_tablero])

                # Inactivar todas las tareas relacionadas
                cursor.execute("UPDATE tareas SET estado = 'I' WHERE id_tablero = %s AND estado = 'A'", [id_tablero])

            return JsonResponse({'mensaje': 'Tablero y tareas inactivados correctamente'}, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

