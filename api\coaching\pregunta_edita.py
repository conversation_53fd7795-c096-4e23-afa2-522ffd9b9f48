
from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.db import connection
import json
from datetime import datetime

@method_decorator(csrf_exempt, name='dispatch')
class PreguntaDeleteView(View):
    def post(self, request):
        try:
            body = json.loads(request.body)
            id = body.get('id')

            if not id:
                return JsonResponse({'error': 'ID es requerido para eliminar'}, status=400)

            with connection.cursor() as cursor:
                cursor.execute("delete from preguntas_encuesta where id = %s", [id])

            return JsonResponse({'mensaje': 'Pregunta eliminada correctamente'}, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
        
@method_decorator(csrf_exempt, name='dispatch')
class OpcionesDeleteView(View):
    def post(self, request):
        try:
            body = json.loads(request.body)
            id = body.get('pregunta_id')

            if not id:
                return JsonResponse({'error': 'ID es requerido para eliminar'}, status=400)

            with connection.cursor() as cursor:
                cursor.execute("delete from opciones_respuesta_encuesta where pregunta_id = %s", [id])

            return JsonResponse({'mensaje': 'Opciones eliminada correctamente'}, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
        
@method_decorator(csrf_exempt, name='dispatch')
class RespuestasDeleteView(View):
    def post(self, request):
        try:
            body = json.loads(request.body)
            id = body.get('encuesta_id')

            if not id:
                return JsonResponse({'error': 'ID es requerido para eliminar'}, status=400)

            with connection.cursor() as cursor:
                cursor.execute("delete from respuestas_encuesta_usuario where encuesta_id = %s", [id])

            return JsonResponse({'mensaje': 'Respuesta eliminada correctamente'}, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class PreguntaCreateView(View):
    def post(self, request):
        try:
            body = json.loads(request.body)

            descripcion = body.get('descripcion')
            enunciado = body.get('enunciado')
            es_obligatoria = body.get('es_obligatoria')
            estado = body.get('estado', 'A')  # Valor por defecto 'A'
            orden = body.get('orden')
            tipo = body.get('tipo')
            encuesta_id = body.get('encuesta_id')

            if not all([enunciado, orden, tipo, encuesta_id]):
                return JsonResponse({'error': 'Faltan campos obligatorios'}, status=400)

            es_obligatoria_bit = 1 if es_obligatoria else 0
            now = datetime.now()

            with connection.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO preguntas_encuesta (
                        descripcion,
                        enunciado,
                        es_obligatoria,
                        estado,
                        fecha_actualizacion,
                        fecha_creacion,
                        orden,
                        tipo,
                        encuesta_id
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, [
                    descripcion,
                    enunciado,
                    es_obligatoria_bit,
                    estado,
                    now,
                    now,
                    orden,
                    tipo,
                    encuesta_id
                ])

                pregunta_id = cursor.lastrowid  # ✅ obtiene el ID generado

            return JsonResponse({'mensaje': 'Pregunta creada correctamente', 'id': pregunta_id}, status=201)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)



@method_decorator(csrf_exempt, name='dispatch')
class PreguntaUpdateView(View):
    def post(self, request):
        try:
            body = json.loads(request.body)

            pregunta_id = body.get('id')
            descripcion = body.get('descripcion')
            enunciado = body.get('enunciado')
            es_obligatoria = body.get('es_obligatoria')
            estado = body.get('estado')
            orden = body.get('orden')
            tipo = body.get('tipo')
            encuesta_id = body.get('encuesta_id')

            if not pregunta_id:
                return JsonResponse({'error': 'El ID de la pregunta es obligatorio'}, status=400)

            # Puedes validar los campos obligatorios si lo deseas (enunciado, estado, etc.)
            now = datetime.now()
            es_obligatoria_bit = 1 if es_obligatoria else 0

            with connection.cursor() as cursor:
                cursor.execute("""
                    UPDATE preguntas_encuesta
                    SET
                        descripcion = %s,
                        enunciado = %s,
                        es_obligatoria = %s,
                        orden = %s,
                        tipo = %s
                    WHERE id = %s
                """, [
                    descripcion,
                    enunciado,
                    es_obligatoria_bit,
                    orden,
                    tipo,
                    pregunta_id
                ])

            return JsonResponse({'mensaje': 'Pregunta actualizada correctamente'}, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


from datetime import datetime

@method_decorator(csrf_exempt, name='dispatch')
class OpcionRespuestaCreateView(View):
    def post(self, request):
        try:
            body = json.loads(request.body)

            texto = body.get('texto')
            valor = body.get('valor')
            pregunta_id = body.get('pregunta_id')

            if not all([texto, pregunta_id]):
                return JsonResponse({'error': 'Faltan campos requeridos'}, status=400)

            fecha_actualizacion = datetime.now()
            fecha_creacion = datetime.now()
            orden = 1  # Puedes cambiar esto si el orden es dinámico

            with connection.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO opciones_respuesta_encuesta (
                        estado,
                        fecha_actualizacion,
                        fecha_creacion,
                        orden,
                        texto,
                        valor,
                        pregunta_id
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, [
                    'A',
                    fecha_actualizacion,
                    fecha_creacion,
                    orden,
                    texto,
                    valor,
                    pregunta_id
                ])

            return JsonResponse({'mensaje': 'Opción creada correctamente'}, status=201)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class OpcionRespuestaUpdateView(View):
    def post(self, request):
        try:
            body = json.loads(request.body)

            id = body.get('id')
            estado = body.get('estado')
            orden = body.get('orden')
            texto = body.get('texto')
            valor = body.get('valor')
            pregunta_id = body.get('pregunta_id')

            if not id:
                return JsonResponse({'error': 'El ID es obligatorio para actualizar'}, status=400)

            now = datetime.now()

            with connection.cursor() as cursor:
                cursor.execute("""
                    UPDATE opciones_respuesta_encuesta
                    SET
                        texto = %s,
                        valor = %s
                    WHERE id = %s
                """, [
                    texto,
                    valor,
                    id
                ])

                if cursor.rowcount == 0:
                    return JsonResponse({'error': 'No se encontró la opción con ese ID'}, status=404)

            return JsonResponse({'mensaje': 'Opción actualizada correctamente'}, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class OpcionesDeletePorIdView(View):
    def post(self, request):
        try:
            body = json.loads(request.body)
            id = body.get('id')

            if not id:
                return JsonResponse({'error': 'ID es requerido para eliminar'}, status=400)

            with connection.cursor() as cursor:
                cursor.execute("delete from opciones_respuesta_encuesta where id = %s", [id])

            return JsonResponse({'mensaje': 'Opciones eliminada correctamente por ID'}, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)