from django.views import View
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.db import connection
import json

@method_decorator(csrf_exempt, name='dispatch')
class VentasPorAsesorView(View):
    def post(self, request):
        try:
            # Parseo del cuerpo de la solicitud
            data = json.loads(request.body)
            fecha_desde_raw = data.get('fecha_desde')  # formato esperado: YYYY-MM-DD
            fecha_hasta_raw = data.get('fecha_hasta')  # formato esperado: YYYY-MM-DD
            sede_id = data.get('sede_id')

            # Validación de parámetros
            if not fecha_desde_raw or not fecha_hasta_raw or sede_id is None:
                return JsonResponse({'success': False, 'error': 'Parámetros requeridos: fecha_desde, fecha_hasta, sede_id'}, status=400)

            # Ajuste de horas para cubrir todo el rango del día
            fecha_desde = fecha_desde_raw + " 00:00:00"
            fecha_hasta = fecha_hasta_raw + " 23:59:59"

            # Consulta SQL
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT 
                        u.codi_usuario AS id_asesor,
                        CONCAT(u.nombre, ' ', u.apellido) AS asesor,
                        CONCAT(c.nombre, ' ', c.apellido) AS coordinador,
                        s.nombre AS sede,
                        COUNT(v.idVenta) AS total_ventas
                    FROM ventas v
                    JOIN usuarios u ON v.idUsuario = u.codi_usuario
                    LEFT JOIN usuarios c ON u.coordinador_id = c.codi_usuario
                    LEFT JOIN sedes s ON u.sede_id = s.id
                    WHERE u.role = 'ASESOR'
                      AND u.estado = 'A'
                      AND v.fechaRegistro BETWEEN %s AND %s
                      AND s.id = %s
                    GROUP BY u.codi_usuario, u.nombre, u.apellido, c.nombre, c.apellido, s.nombre
                    ORDER BY total_ventas DESC
                """, [fecha_desde, fecha_hasta, sede_id])

                # Conversión de resultados
                columns = [col[0] for col in cursor.description]
                results = [dict(zip(columns, row)) for row in cursor.fetchall()]

            return JsonResponse({'success': True, 'data': results}, status=200)

        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)
