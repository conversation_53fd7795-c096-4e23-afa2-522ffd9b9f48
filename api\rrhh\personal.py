from django.db import connection
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status

@api_view(['GET'])
def listar_personal_general(request):
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                  SELECT
                        p.id_personal,
                        e.razon_social,
                        p.tipo_documento,
                        p.documento,
                        p.apellidos_nombres_completos,
                        p.fecha_nacimiento,
                        p.edad,
                        p.cargo,
                        p.sede,
                        c.fecha_inicio,
                        c.fecha_fin,
                        c.ruta_archivo
                    FROM personal p
                    JOIN empresa e 
                    ON p.id_empresa = e.id_empresa
                    LEFT JOIN contrato c
                    ON c.id_contrato = (
                        SELECT c2.id_contrato
                        FROM contrato c2
                        WHERE c2.id_personal = p.id_personal
                        ORDER BY c2.fecha_inicio DESC,       -- más reciente primero
                                    c2.fecha_fin DESC,          -- desempate opcional
                                    c2.id_contrato DESC         -- desempate final
                        LIMIT 1
                        )
                    ORDER BY p.id_personal DESC;
                                """)
            results = cursor.fetchall()
            data = []
            for row in results:
                data.append({
                    'id_personal': row[0],
                    'razon_social': row[1],
                    'tipo_documento': row[2],
                    'documento': row[3],
                    'apellidos_nombres_completos': row[4],
                    'fecha_nacimiento': row[5],
                    'edad': row[6],
                    'cargo': row[7],
                    'sede': row[8],
                    'fecha_inicio': row[9],
                    'fecha_fin': row[10],
                    'ruta_archivo': row[11]
                })
        return Response(data, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def listar_personal_por_id(request, id_personal):
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    p.*,
                    e.ruc,
                    e.razon_social,
                    e.direccion as direccion_empresa,
                    e.departamento as departamento_empresa,
                    e.provincia as provincia_empresa,
                    e.distrito as distrito_empresa
                FROM
                    personal p
                JOIN
                    empresa e ON p.id_empresa = e.id_empresa
                WHERE
                    p.id_personal = %s
            """, [id_personal])
            results = cursor.fetchone()
            if results:
                data = {
                    'id_personal': results[0],
                    'id_empresa': results[1],
                    'primer_nombre': results[2],
                    'segundo_nombre': results[3],
                    'apellido_paterno': results[4],
                    'apellido_materno': results[5],
                    'tipo_documento': results[6],
                    'documento': results[7],
                    'apellidos_nombres_completos': results[8],
                    'tipo_contrato': results[9],
                    'fecha_incorporacion': results[10],
                    'fecha_cese': results[11],
                    'fecha_nacimiento': results[12],
                    'edad': results[13],
                    'direccion': results[14],
                    'telefono_movil': results[15],
                    'correo_electronico': results[16],
                    'cargo': results[17],
                    'sistema_pensiones': results[18],
                    'nombre_institucion_educativa': results[19],
                    'carrera_profesional': results[20],
                    'institucion_bancaria': results[21],
                    'cuenta_sueldo': results[22],
                    'horario': results[23],
                    'salario': results[24],
                    'condicion_trabajo_alimentacion': results[25],
                    'break_time': results[26],
                    'asignacion_familiar': results[27],
                    'importe': results[28],
                    'motivo_cese': results[29],
                    'importe_liquidacion': results[30],
                    'genero': results[31],
                    'coordinador': results[32],
                    'tiempo_servicio': results[33],
                    'inicio_contrato': results[34],
                    'fin_contrato': results[35],
                    'fecha_vencimiento_periodo_prueba': results[36],
                    'digito_verificador': results[37],
                    'respuesta_rit': results[38],
                    'confidencialidad': results[39],
                    'carga_familiar': results[40],
                    'legalizacion_mapfre': results[41],

                    'departamento': results[42],
                    'provincia': results[43],
                    'distrito': results[44],
                    'categoria_contrato': results[45],
                    'sede': results[46],

                    'ruc': results[47],
                    'razon_social': results[48],
                    'direccion_empresa': results[49],
                    'departamento_empresa': results[50],
                    'provincia_empresa': results[51],
                    'distrito_empresa': results[52],
                }
                return Response(data, status=status.HTTP_200_OK)
            return Response({'mensaje': 'Personal no encontrado'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
def crear_personal(request):
    data = request.data
    
    # Validar que id_empresa no sea nulo o vacío
    if not data.get('id_empresa'):
        return Response({'error': 'El campo id_empresa es obligatorio.'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        with connection.cursor() as cursor:
            # Lista de todos los campos que espera la consulta SQL
            fields = [
                'id_empresa', 'primer_nombre', 'segundo_nombre', 'apellido_paterno', 'apellido_materno',
                'tipo_documento', 'documento', 'apellidos_nombres_completos', 'tipo_contrato',
                'fecha_incorporacion', 'fecha_nacimiento', 'edad', 'direccion', 'telefono_movil',
                'correo_electronico', 'cargo', 'sistema_pensiones', 'nombre_institucion_educativa',
                'carrera_profesional', 'institucion_bancaria', 'cuenta_sueldo', 'horario', 'salario',
                'condicion_trabajo_alimentacion', 'break_time', 'asignacion_familiar', 'importe',
                'motivo_cese', 'importe_liquidacion', 'genero', 'coordinador', 'tiempo_servicio',
                'inicio_contrato', 'fin_contrato', 'fecha_vencimiento_periodo_prueba',
                'digito_verificador', 'respuesta_rit', 'confidencialidad', 'carga_familiar',
                'legalizacion_mapfre', 'departamento', 'provincia', 'distrito', 'categoria_contrato', 'sede'
            ]
            
            # Procesar valores: convertir cadenas vacías a None (NULL)
            values = []
            for field in fields:
                value = data.get(field)
                if value == '':
                    values.append(None)
                else:
                    values.append(value)

            # La consulta SQL no cambia
            sql_query = """
                INSERT INTO personal (
                    id_empresa, primer_nombre, segundo_nombre, apellido_paterno, apellido_materno,
                    tipo_documento, documento, apellidos_nombres_completos, tipo_contrato,
                    fecha_incorporacion, fecha_nacimiento, edad, direccion, telefono_movil,
                    correo_electronico, cargo, sistema_pensiones, nombre_institucion_educativa,
                    carrera_profesional, institucion_bancaria, cuenta_sueldo, horario, salario,
                    condicion_trabajo_alimentacion, break_time, asignacion_familiar, importe,
                    motivo_cese, importe_liquidacion, genero, coordinador, tiempo_servicio,
                    inicio_contrato, fin_contrato, fecha_vencimiento_periodo_prueba,
                    digito_verificador, respuesta_rit, confidencialidad, carga_familiar,
                    legalizacion_mapfre, departamento, provincia, distrito, categoria_contrato, sede
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s
                )
            """
            
            cursor.execute(sql_query, values)
            
        return Response({'mensaje': 'Personal creado correctamente'}, status=status.HTTP_201_CREATED)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['PUT'])
def actualizar_personal(request, id_personal):
    data = request.data
    try:
        with connection.cursor() as cursor:
            # Lista de campos en el orden que aparecen en la consulta UPDATE
            fields = [
                'id_empresa', 'primer_nombre', 'segundo_nombre', 'apellido_paterno',
                'apellido_materno', 'tipo_documento', 'documento',
                'apellidos_nombres_completos', 'tipo_contrato', 'fecha_incorporacion',
                'fecha_cese', 'fecha_nacimiento', 'edad', 'direccion',
                'telefono_movil', 'correo_electronico', 'cargo', 'sistema_pensiones',
                'nombre_institucion_educativa', 'carrera_profesional',
                'institucion_bancaria', 'cuenta_sueldo', 'horario', 'salario',
                'condicion_trabajo_alimentacion', 'break_time', 'asignacion_familiar',
                'importe', 'motivo_cese', 'importe_liquidacion', 'genero',
                'coordinador', 'tiempo_servicio', 'inicio_contrato', 'fin_contrato',
                'fecha_vencimiento_periodo_prueba', 'digito_verificador',
                'respuesta_rit', 'confidencialidad', 'carga_familiar',
                'legalizacion_mapfre', 'departamento', 'provincia', 'distrito', 'categoria_contrato', 'sede'
            ]

            # Procesar valores: convertir cadenas vacías a None (NULL)
            values = []
            for field in fields:
                value = data.get(field)
                if value == '':
                    values.append(None)
                else:
                    values.append(value)
            
            # Añadir el id_personal al final para la cláusula WHERE
            values.append(id_personal)

            # La consulta SQL no cambia
            sql_query = """
                UPDATE personal SET
                    id_empresa = %s, primer_nombre = %s, segundo_nombre = %s, apellido_paterno = %s,
                    apellido_materno = %s, tipo_documento = %s, documento = %s,
                    apellidos_nombres_completos = %s, tipo_contrato = %s, fecha_incorporacion = %s,
                    fecha_cese = %s, fecha_nacimiento = %s, edad = %s, direccion = %s,
                    telefono_movil = %s, correo_electronico = %s, cargo = %s, sistema_pensiones = %s,
                    nombre_institucion_educativa = %s, carrera_profesional = %s,
                    institucion_bancaria = %s, cuenta_sueldo = %s, horario = %s, salario = %s,
                    condicion_trabajo_alimentacion = %s, break_time = %s, asignacion_familiar = %s,
                    importe = %s, motivo_cese = %s, importe_liquidacion = %s, genero = %s,
                    coordinador = %s, tiempo_servicio = %s, inicio_contrato = %s, fin_contrato = %s,
                    fecha_vencimiento_periodo_prueba = %s, digito_verificador = %s,
                    respuesta_rit = %s, confidencialidad = %s, carga_familiar = %s,
                    legalizacion_mapfre = %s, departamento = %s, provincia = %s, distrito = %s, categoria_contrato = %s, sede = %s
                WHERE id_personal = %s
            """
            
            cursor.execute(sql_query, values)

        return Response({'mensaje': 'Personal actualizado correctamente'}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
