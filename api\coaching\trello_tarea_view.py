from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.db import connection
import json

@method_decorator(csrf_exempt, name='dispatch')
class TareaCrearView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            titulo = data.get('titulo')
            id_lista = data.get('id_lista')
            creado_por = data.get('creado_por')
            tablero_id = data.get('tablero_id')

            if not all([titulo, id_lista, creado_por]):
                return JsonResponse({'error': 'Faltan campos obligatorios'}, status=400)

            with connection.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO tareas (titulo, id_lista, creado_por, id_tablero)
                    VALUES (%s, %s, %s, %s)
                """, [titulo, id_lista, creado_por, tablero_id])

            return JsonResponse({'mensaje': 'Tarea creada correctamente'}, status=201)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class TareaListarPorListaView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            id_lista = data.get('id_lista')

            if not id_lista:
                return JsonResponse({'error': 'id_lista es obligatorio'}, status=400)

            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT id_tarea, titulo, descripcion, asignado_a, estado
                    FROM tareas
                    WHERE id_lista = %s AND estado = 'A'
                """, [id_lista])
                filas = cursor.fetchall()

                tareas = [
                    {
                        'id': fila[0],
                        'titulo': fila[1],
                        'descripcion': fila[2],
                        'asignado_a': fila[3],
                        'estado': fila[4]
                    } for fila in filas
                ]

            return JsonResponse({'tareas': tareas}, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class TareaEditarView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            id_tarea = data.get('id_tarea')
            titulo = data.get('titulo')
            descripcion = data.get('descripcion')
            tipo_asignacion = data.get('tipo_asignacion')
            asignado_a = data.get('asignado_a')    

            if not id_tarea:
                return JsonResponse({'error': 'id_tarea es obligatorio'}, status=400)

            with connection.cursor() as cursor:
                cursor.execute("""
                    UPDATE tareas SET titulo = %s, descripcion = %s,  tipo_asignacion = %s, asignado_a = %s  WHERE id_tarea = %s
                """, [titulo, descripcion, tipo_asignacion, asignado_a, id_tarea])

            return JsonResponse({'mensaje': 'Tarea editada correctamente'}, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class TareaEliminarView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            id_tarea = data.get('id_tarea')

            if not id_tarea:
                return JsonResponse({'error': 'id_tarea es obligatorio'}, status=400)

            with connection.cursor() as cursor:
                cursor.execute("""
                    UPDATE tareas SET estado = 'I' WHERE id_tarea = %s
                """, [id_tarea])

            return JsonResponse({'mensaje': 'Tarea inactivada correctamente'}, status=200)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
