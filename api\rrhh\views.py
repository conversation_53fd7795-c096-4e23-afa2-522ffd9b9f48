from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

@method_decorator(csrf_exempt, name='dispatch')
class TestRRHHView(View):
    def get(self, request):
        return JsonResponse({'message': 'Hello from RRHH API!'})

    def post(self, request):
        return JsonResponse({'message': 'POST request received by RRHH API!'})
