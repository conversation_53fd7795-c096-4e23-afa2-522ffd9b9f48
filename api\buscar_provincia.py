from django.http import JsonResponse
from django.views import View
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
import json
from django.db import connection

@method_decorator(csrf_exempt, name='dispatch')  # solo si no usas token CSRF
class BuscarProvinciaView(View):
    def post(self, request):
        try:
            data = json.loads(request.body.decode('utf-8'))
            direccion = data.get('provincia', '').strip()
        except (json.JSONDecodeError, UnicodeDecodeError):
            return JsonResponse({'error': 'Formato JSON inválido'}, status=400)

        if not direccion:
            return JsonResponse({'error': 'El campo "provincia" es requerido'}, status=400)

        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT *
                FROM (
                -- Ventas reales
                SELECT 
                    v.nombres_apellidos COLLATE utf8mb4_spanish2_ci AS nombres_apellidos,
                    dv.direccion COLLATE utf8mb4_spanish2_ci AS direccion,
                    v.promocion COLLATE utf8mb4_spanish2_ci AS plan_actual,
                    v.tipoFibra COLLATE utf8mb4_spanish2_ci AS tipo_fibra,
                    IFNULL(v.Estado, 'LEAD') COLLATE utf8mb4_spanish2_ci AS estado
                FROM ventas v
                JOIN direccionesVentas dv 
                    ON v.codigoVenta COLLATE utf8mb4_0900_ai_ci = dv.codigoVenta

                UNION ALL

                -- Leads
                SELECT 
                    cr.nombres_apellidos COLLATE utf8mb4_spanish2_ci,
                    cr.direccion COLLATE utf8mb4_spanish2_ci,
                    cr.plan_actual COLLATE utf8mb4_spanish2_ci,
                    cr.tipo_fibra COLLATE utf8mb4_spanish2_ci,
                    'LEAD' COLLATE utf8mb4_spanish2_ci
                FROM cliente_residencial cr
                ) AS tmp
                WHERE LOWER(
                REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(tmp.direccion, 'á', 'a'), 'é', 'e'), 'í', 'i'), 'ó', 'o'), 'ú', 'u')
                ) LIKE %s
            """, [f"%{direccion.lower()}%"])
            columns = [col[0] for col in cursor.description]
            results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        return JsonResponse(results, safe=False)
