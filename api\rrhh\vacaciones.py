from django.db import connection
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status

@api_view(['GET'])
def listar_vacaciones_por_personal_anio(request, id_personal, anio_seleccionado):
    """
    Lista todas las vacaciones de un personal específico por año
    """
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    v.id_vacaciones_personal,
                    v.id_personal,
                    v.anio_seleccionado,
                    v.dias_asignados,
                    v.dias_disponibles,
                    v.dias_utilizados,
                    v.dias_pendientes,
                    v.tipo_vacaciones,
                    v.fecha_inicio,
                    v.fecha_fin,
                    v.dias_solicitados,
                    v.motivo,
                    v.dias_comprados,
                    v.precio_por_dia,
                    v.total_compra,
                    v.observaciones,
                    v.estado,
                    p.apellidos_nombres_completos,
                    p.documento as personal_documento
                FROM
                    vacaciones_personal v
                JOIN
                    personal p ON v.id_personal = p.id_personal
                WHERE
                    v.id_personal = %s AND v.anio_seleccionado = %s
                ORDER BY v.id_vacaciones_personal DESC
            """, [id_personal, anio_seleccionado])
            results = cursor.fetchall()
            data = []
            for row in results:
                data.append({
                    'id_vacaciones_personal': row[0],
                    'id_personal': row[1],
                    'anio_seleccionado': row[2],
                    'dias_asignados': row[3],
                    'dias_disponibles': row[4],
                    'dias_utilizados': row[5],
                    'dias_pendientes': row[6],
                    'tipo_vacaciones': row[7],
                    'fecha_inicio': row[8],
                    'fecha_fin': row[9],
                    'dias_solicitados': row[10],
                    'motivo': row[11],
                    'dias_comprados': row[12],
                    'precio_por_dia': row[13],
                    'total_compra': row[14],
                    'observaciones': row[15],
                    'estado': row[16],
                    'apellidos_nombres_completos': row[17],
                    'personal_documento': row[18]
                })
        return Response(data, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def obtener_vacacion_por_id(request, id_vacaciones_personal):
    """
    Obtiene una vacación específica por su ID
    """
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    v.*,
                    p.apellidos_nombres_completos,
                    p.documento as personal_documento,
                    p.cargo
                FROM
                    vacaciones_personal v
                JOIN
                    personal p ON v.id_personal = p.id_personal
                WHERE
                    v.id_vacaciones_personal = %s
            """, [id_vacaciones_personal])
            result = cursor.fetchone()
            if result:
                data = {
                    'id_vacaciones_personal': result[0],
                    'id_personal': result[1],
                    'anio_seleccionado': result[2],
                    'dias_asignados': result[3],
                    'dias_disponibles': result[4],
                    'dias_utilizados': result[5],
                    'dias_pendientes': result[6],
                    'tipo_vacaciones': result[7],
                    'fecha_inicio': result[8],
                    'fecha_fin': result[9],
                    'dias_solicitados': result[10],
                    'motivo': result[11],
                    'dias_comprados': result[12],
                    'precio_por_dia': result[13],
                    'total_compra': result[14],
                    'observaciones': result[15],
                    'estado': result[16],
                    'apellidos_nombres_completos': result[17],
                    'personal_documento': result[18],
                    'cargo': result[19]
                }
                return Response(data, status=status.HTTP_200_OK)
            return Response({'mensaje': 'Vacación no encontrada'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
def crear_vacacion(request):
    """
    Crea una nueva vacación
    """
    data = request.data
    
    # Validar campos obligatorios
    campos_obligatorios = ['id_personal', 'anio_seleccionado', 'tipo_vacaciones']
    
    for campo in campos_obligatorios:
        if not data.get(campo):
            return Response({'error': f'El campo {campo} es obligatorio.'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        with connection.cursor() as cursor:
            # Lista de todos los campos
            fields = [
                'id_personal', 'anio_seleccionado', 'dias_asignados', 'dias_disponibles',
                'dias_utilizados', 'dias_pendientes', 'tipo_vacaciones', 'fecha_inicio',
                'fecha_fin', 'dias_solicitados', 'motivo', 'dias_comprados',
                'precio_por_dia', 'total_compra', 'observaciones', 'estado'
            ]
            
            # Procesar valores: convertir cadenas vacías a None (NULL)
            values = []
            for field in fields:
                value = data.get(field)
                if value == '':
                    values.append(None)
                else:
                    values.append(value)

            sql_query = """
                INSERT INTO vacaciones_personal (
                    id_personal, anio_seleccionado, dias_asignados, dias_disponibles,
                    dias_utilizados, dias_pendientes, tipo_vacaciones, fecha_inicio,
                    fecha_fin, dias_solicitados, motivo, dias_comprados,
                    precio_por_dia, total_compra, observaciones, estado
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
            """
            
            cursor.execute(sql_query, values)
            
        return Response({'mensaje': 'Vacación creada correctamente'}, status=status.HTTP_201_CREATED)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['PUT'])
def actualizar_vacacion(request, id_vacaciones_personal):
    """
    Actualiza una vacación existente
    """
    data = request.data
    try:
        with connection.cursor() as cursor:
            # Lista de campos en el orden que aparecen en la consulta UPDATE
            fields = [
                'id_personal', 'anio_seleccionado', 'dias_asignados', 'dias_disponibles',
                'dias_utilizados', 'dias_pendientes', 'tipo_vacaciones', 'fecha_inicio',
                'fecha_fin', 'dias_solicitados', 'motivo', 'dias_comprados',
                'precio_por_dia', 'total_compra', 'observaciones', 'estado'
            ]

            # Procesar valores: convertir cadenas vacías a None (NULL)
            values = []
            for field in fields:
                value = data.get(field)
                if value == '':
                    values.append(None)
                else:
                    values.append(value)
            
            # Añadir el id_vacaciones_personal al final para la cláusula WHERE
            values.append(id_vacaciones_personal)

            sql_query = """
                UPDATE vacaciones_personal SET
                    id_personal = %s, anio_seleccionado = %s, dias_asignados = %s, dias_disponibles = %s,
                    dias_utilizados = %s, dias_pendientes = %s, tipo_vacaciones = %s, fecha_inicio = %s,
                    fecha_fin = %s, dias_solicitados = %s, motivo = %s, dias_comprados = %s,
                    precio_por_dia = %s, total_compra = %s, observaciones = %s, estado = %s
                WHERE id_vacaciones_personal = %s
            """
            
            cursor.execute(sql_query, values)

        return Response({'mensaje': 'Vacación actualizada correctamente'}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['DELETE'])
def eliminar_vacacion(request, id_vacaciones_personal):
    """
    Elimina una vacación
    """
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                DELETE FROM vacaciones_personal WHERE id_vacaciones_personal = %s
            """, [id_vacaciones_personal])
            
            if cursor.rowcount == 0:
                return Response({'mensaje': 'Vacación no encontrada'}, status=status.HTTP_404_NOT_FOUND)
                
        return Response({'mensaje': 'Vacación eliminada correctamente'}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def resumen_vacaciones_personal(request, id_personal, anio_seleccionado):
    """
    Obtiene un resumen de vacaciones por personal y año (días totales, utilizados, disponibles)
    """
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    SUM(dias_asignados) as total_asignados,
                    SUM(dias_utilizados) as total_utilizados,
                    SUM(dias_disponibles) as total_disponibles,
                    SUM(dias_pendientes) as total_pendientes,
                    SUM(CASE WHEN tipo_vacaciones = 'COMPRADAS' THEN dias_comprados ELSE 0 END) as total_comprados,
                    SUM(CASE WHEN tipo_vacaciones = 'COMPRADAS' THEN total_compra ELSE 0 END) as monto_total_compras,
                    COUNT(*) as total_registros,
                    p.apellidos_nombres_completos,
                    p.documento
                FROM
                    vacaciones_personal v
                JOIN
                    personal p ON v.id_personal = p.id_personal
                WHERE
                    v.id_personal = %s AND v.anio_seleccionado = %s
                GROUP BY v.id_personal, p.apellidos_nombres_completos, p.documento
            """, [id_personal, anio_seleccionado])
            result = cursor.fetchone()
            
            if result:
                data = {
                    'id_personal': id_personal,
                    'anio_seleccionado': anio_seleccionado,
                    'total_asignados': result[0] or 0,
                    'total_utilizados': result[1] or 0,
                    'total_disponibles': result[2] or 0,
                    'total_pendientes': result[3] or 0,
                    'total_comprados': result[4] or 0,
                    'monto_total_compras': result[5] or 0,
                    'total_registros': result[6] or 0,
                    'apellidos_nombres_completos': result[7],
                    'documento': result[8]
                }
                return Response(data, status=status.HTTP_200_OK)
            else:
                return Response({
                    'id_personal': id_personal,
                    'anio_seleccionado': anio_seleccionado,
                    'total_asignados': 0,
                    'total_utilizados': 0,
                    'total_disponibles': 0,
                    'total_pendientes': 0,
                    'total_comprados': 0,
                    'monto_total_compras': 0,
                    'total_registros': 0,
                    'mensaje': 'No se encontraron registros de vacaciones para este personal y año'
                }, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def listado_personal_vacaciones(request, anio):
    """
    Lista todo el personal con cálculos de vacaciones por año
    """
    try:
        with connection.cursor() as cursor:
            # Debug específico para el usuario 6
            cursor.execute("""
                SELECT id_personal, anio_seleccionado, tipo_vacaciones, dias_solicitados
                FROM vacaciones_personal
                WHERE id_personal = 6
                ORDER BY anio_seleccionado, id_vacaciones_personal
            """)
            debug_user_data = cursor.fetchall()
            print(f"DEBUG: Todos los datos del usuario 6: {debug_user_data}")

            cursor.execute("""
                SELECT id_personal, anio_seleccionado, tipo_vacaciones, dias_solicitados
                FROM vacaciones_personal
                WHERE id_personal = 6 AND anio_seleccionado = %s
            """, [anio])
            debug_filtered = cursor.fetchall()
            print(f"DEBUG: Datos del usuario 6 filtrados por año {anio}: {debug_filtered}")

            # Obtener datos básicos del personal
            cursor.execute("""
                SELECT
                    id_personal,
                    tipo_documento,
                    documento,
                    apellidos_nombres_completos,
                    fecha_incorporacion,
                    sede,
                    DATEDIFF(CURDATE(), fecha_incorporacion) as dias_servicio
                FROM
                    personal p
                ORDER BY
                    apellidos_nombres_completos
            """)

            personal_results = cursor.fetchall()
            data = []

            for row in personal_results:
                id_personal = row[0]

                # Obtener suma de días para este empleado y año específico
                cursor.execute("""
                    SELECT COALESCE(SUM(dias_solicitados), 0)
                    FROM vacaciones_personal
                    WHERE id_personal = %s
                    AND anio_seleccionado = %s
                    
                """, [id_personal, anio])

                dias_result = cursor.fetchone()
                dias_utilizados = float(dias_result[0]) if dias_result else 0.0

                # Calcular tiempo de servicio
                dias_servicio = row[6]
                anos = dias_servicio // 365
                meses = (dias_servicio % 365) // 30
                dias = (dias_servicio % 365) % 30
                tiempo_servicio = f"{anos} años {meses} meses {dias} días"

                # Determinar estado de vacaciones
                if dias_servicio < 365:
                    vacaciones_de_ley = 'NO CORRESPONDE'
                elif dias_utilizados == 15:
                    vacaciones_de_ley = 'COMPLETAS'
                else:
                    vacaciones_de_ley = 'SI CORRESPONDE'

                data.append({
                    'id_personal': row[0],
                    'tipo_documento': row[1],
                    'documento': row[2],
                    'apellidos_nombres_completos': row[3],
                    'fecha_incorporacion': row[4],
                    'sede': row[5],
                    'tiempo_servicio': tiempo_servicio,
                    'dias_asignados': 15,
                    'dias_utilizados': dias_utilizados,
                    'dias_disponibles': 15 - dias_utilizados,
                    'vacaciones_de_ley': vacaciones_de_ley
                })

            # Calcular totales adicionales incluyendo COMPLETAS
            total_corresponden = sum(1 for item in data if item['vacaciones_de_ley'] == 'SI CORRESPONDE')
            total_no_corresponden = sum(1 for item in data if item['vacaciones_de_ley'] == 'NO CORRESPONDE')
            total_completas = sum(1 for item in data if item['vacaciones_de_ley'] == 'COMPLETAS')

            # Calcular totales por sede
            totales_por_sede = {}
            for item in data:
                sede = item['sede'] or 'Sin sede'  # Manejar valores nulos
                if sede in totales_por_sede:
                    totales_por_sede[sede] += 1
                else:
                    totales_por_sede[sede] = 1

            return Response({
                'anio_consultado': anio,
                'total_personal': len(data),
                'total_corresponden': total_corresponden,
                'total_no_corresponden': total_no_corresponden,
                'total_completas': total_completas,
                'totales_por_sede': totales_por_sede,
                'personal': data
            }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
